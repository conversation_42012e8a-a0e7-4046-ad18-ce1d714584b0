/**
 * Manager data structure for profile display
 */
export interface IManagerProfile {
  id: number;
  name: string;
  title: string;
  avatar: string;
}

/**
 * Mock data for multiple managers
 */
export const mockManagers: IManagerProfile[] = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    title: 'Head of Customer Service',
    avatar: 'assets/images/user-cage.png',
  },
  {
    id: 2,
    name: '<PERSON>',
    title: 'Regional Sales Manager',
    avatar: 'assets/images/user-taro.jpg',
  },
  {
    id: 3,
    name: '<PERSON>',
    title: 'Regional Sales Manager',
    avatar: 'assets/images/user-cage.png',
  },
];

export const mockBio = {
  name: '<PERSON>, PhD',
  title: 'Customer Service Associate',
  pronouns: 'She/Her/Hers',
  employeeNumber: '1256',
  status: 'Active',
  avatar: 'assets/images/user-cage.png',
  biography:
    'I enjoy being part of an engaging and dynamic work environment. My personal interests include soccer, skiing, and live music. I am also passionate about sustainability and conservation, and am inv...',
  manager: {
    name: '<PERSON><PERSON>',
    title: 'Head of Customer Service',
    others: 2,
  },
  managers: mockManagers,
  location: {
    department: 'Customer Service',
    site: 'Customer Service 10 (US)',
  },
  lengthOfService: {
    value: '2 years and 6 months',
    hireDate: '8/15/2023',
  },
};
