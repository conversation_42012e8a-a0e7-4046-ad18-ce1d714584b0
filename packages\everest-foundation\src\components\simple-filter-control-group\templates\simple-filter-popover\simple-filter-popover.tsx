import React, { PropsWithChildren, useContext } from 'react';

import { IDialogCloseEvent } from '../../../overlay';
import { Popover, PopoverBody } from '../../../popover';
import { SimpleFilterCustomControlContext } from '../simple-filter-custom-control/simple-filter-custom-control.context';

export interface ISimpleFilterPopover {
  id: string;
  testId?: string;
  onClose?: (e: IDialogCloseEvent | undefined) => void;
  placement?: 'bottomLeft' | 'bottomRight';
  onOpen?: () => void;
}

export const SimpleFilterPopover = ({
  id,
  testId,
  children,
  onClose,
  placement = 'bottomLeft',
  onOpen,
}: PropsWithChildren<ISimpleFilterPopover>): JSX.Element => {
  const { open, triggerRef } = useContext(SimpleFilterCustomControlContext);

  const position = React.useMemo(() => {
    if (placement === 'bottomLeft') {
      return {
        anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
        transformOrigin: { vertical: 'top', horizontal: 'left' },
      };
    }
    return {
      anchorOrigin: { vertical: 'bottom', horizontal: 'right' },
      transformOrigin: { vertical: 'top', horizontal: 'right' },
    };
  }, [placement]);

  return (
    <Popover
      id={id}
      testId={testId}
      open={!!open}
      triggerRef={triggerRef}
      offset={{ vertical: '0.25rem' }}
      anchorOrigin={position.anchorOrigin}
      transformOrigin={position.transformOrigin}
      onClose={(e) => {
        onClose?.(e);
        // needed so that the popover closes and focus trap won't block the focus getting assigned back to the trigger
        requestAnimationFrame(() => {
          triggerRef.current?.focus();
        });
      }}
      onOpen={onOpen}
      borderRadius="--evr-radius-xs"
    >
      <PopoverBody id={`${id}-body`}>{children}</PopoverBody>
    </Popover>
  );
};

SimpleFilterPopover.displayName = 'SimpleFilterPopover';
