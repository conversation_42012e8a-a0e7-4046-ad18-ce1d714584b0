import React from 'react';
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { SimpleFilterTagGroup } from './simple-filter-tag-group';

describe('[SimpleFilterTagGroup]', () => {
  const MockTag = () => <div>Mock Tag</div>;
  const getGroup = () => screen.getByTestId(defaultProps.testId);
  const defaultProps = {
    id: 'group-id',
    testId: 'group-test-id',
    onClearAll: jest.fn(),
    textMap: {
      showXMoreButtonLabel: 'showXMoreButtonLabel',
      showLessButtonLabel: 'showLessButtonLabel',
      clearAllButtonLabel: 'clearAllButtonLabel',
      filtersClearedMessage: 'filtersClearedMessage',
    },
  };
  const getClearAllButton = () => screen.getByText(defaultProps.textMap.clearAllButtonLabel);
  const getShowMoreButton = () => screen.getByText(defaultProps.textMap.showXMoreButtonLabel);

  let resizeObserverTrigger: jest.Mock;

  beforeEach(() => {
    resizeObserverTrigger = jest.fn();

    global.ResizeObserver = jest.fn((callback) => {
      resizeObserverTrigger = callback;
      return {
        observe: jest.fn(),
        unobserve: jest.fn(),
        disconnect: jest.fn(),
      };
    }) as jest.Mock;
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  const renderComponent = () =>
    render(
      <div id="container" style={{ width: '300px' }}>
        <SimpleFilterTagGroup {...defaultProps}>
          <MockTag />
        </SimpleFilterTagGroup>
      </div>
    );

  it('renders children', () => {
    renderComponent();

    expect(screen.getByTestId(defaultProps.testId)).toBeInTheDocument();
    expect(screen.queryAllByText('Mock Tag').length).toBe(2); // It's 2 because of the hidden container
    expect(screen.getByText(defaultProps.textMap.clearAllButtonLabel)).toBeInTheDocument();
  });

  it('renders the Clear All button and triggers onClearAll', async () => {
    renderComponent();

    expect(getClearAllButton()).toBeInTheDocument();

    await userEvent.click(getClearAllButton());

    expect(defaultProps.onClearAll).toHaveBeenCalledTimes(1);
  });

  it('renders correct show more button label', async () => {
    const { rerender } = renderComponent();

    act(() => {
      // adjust the widths so that the showMore button will be shown
      const parent = document.getElementById(defaultProps.id);
      Object.defineProperty(parent, 'clientWidth', { value: 300 });
      Object.defineProperty(parent, 'scrollWidth', { value: 400 });
      // trigger resize. SimpleFilterTagGroup doesn't actually read the args from it so we can pass empty object
      resizeObserverTrigger([{}]);
    });

    rerender(
      <div id="container" style={{ width: '300px' }}>
        <SimpleFilterTagGroup {...defaultProps}>
          <MockTag />
        </SimpleFilterTagGroup>
      </div>
    );

    expect(getShowMoreButton()).toBeInTheDocument();
  });

  it('applies aria attributes correctly', () => {
    renderComponent();

    expect(getGroup()).toHaveAttribute('role', 'group');
  });
});
