@use '@ceridianhcm/theme/dist/scss/' as typography;

button.buttonBaseOverrides {
  gap: var(--evr-spacing-3xs);
  color: var(--evr-content-primary-default);
  fill: var(--evr-content-primary-default);
  text-wrap: nowrap;
  
  &.selected {
    background: var(--evr-surfaces-primary-selected);
    padding-inline-start: var(--evr-spacing-2xs);
    color: var(--evr-content-primary-highemp);
    fill: var(--evr-content-primary-highemp);
  }
}

.dropdownTriggerButtonOverrides {
  padding-inline-end: var(--evr-spacing-2xs);
}


.selectionCountBadge {
  @include typography.body2Bold;
  border-radius: var(--evr-radius-3xs);
  padding: 0 var(--evr-spacing-3xs);
  margin-block: calc(-1 * var(--evr-spacing-4xs));
  background: var(--evr-surfaces-secondary-selected);
}