.tag-popover-menu-prototype {
  display: inline-flex;
  align-items: center;
  position: relative;

  // Style the Tag component to look like a primary blue count badge
  :global(.evr-tag) {
    // Override info status to use primary blue colors
    &.info {
      background-color: var(--evr-color-primary-500) !important;
      color: var(--evr-content-primary-inverse) !important;
      border: 1px solid var(--evr-color-primary-500) !important;
      border-radius: var(--evr-border-radius-md) !important;
      
      // Ensure proper sizing for count badge
      padding: var(--evr-spacing-4xs) var(--evr-spacing-xs) !important;
      font-size: var(--evr-font-size-sm) !important;
      font-weight: var(--evr-demi-bold-weight) !important;
      line-height: var(--evr-line-height-sm) !important;
      min-height: auto !important;
      height: auto !important;
      
      // Ensure proper alignment
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;

      &:hover {
        background-color: var(--evr-color-primary-600) !important;
        border-color: var(--evr-color-primary-600) !important;
        color: var(--evr-content-primary-inverse) !important;
      }

      &:focus {
        outline: 2px solid var(--evr-color-focus) !important;
        outline-offset: 2px !important;
        background-color: var(--evr-color-primary-500) !important;
      }

      &:active {
        background-color: var(--evr-color-primary-700) !important;
        border-color: var(--evr-color-primary-700) !important;
      }
    }

    // Ensure clickable state is properly styled
    &.clickable {
      cursor: pointer;
      
      &:disabled {
        opacity: var(--evr-opacity-disabled);
        pointer-events: none;
        background-color: var(--evr-color-neutral-300) !important;
        color: var(--evr-content-primary-disabled) !important;
      }
    }
  }

  // Popover header styling
  &__header {
    padding: var(--evr-spacing-sm) var(--evr-spacing-md);
    border-bottom: 1px solid var(--evr-color-neutral-200);
    background-color: var(--evr-color-neutral-0);

    h4 {
      margin: 0;
      color: var(--evr-content-primary-default);
    }
  }

  // Spacing classes for consistent positioning (following Contact Information standards)
  &--spacing-xs {
    margin-left: var(--evr-spacing-xs);
  }

  &--spacing-sm {
    margin-left: var(--evr-spacing-sm);
  }

  &--spacing-md {
    margin-left: var(--evr-spacing-md);
  }
}

// Global styles for MenuList items within the popover
.tag-popover-menu-prototype {
  // Style menu list items for better manager display
  :global(.evr-menu-list) {
    max-height: 300px;
    overflow-y: auto;
    padding: 0;
    margin: 0;

    :global(.evr-menu-list-item-base) {
      padding: var(--evr-spacing-xs) var(--evr-spacing-sm);
      
      // Ensure proper spacing for MediaObject within menu items
      .evr-media-object {
        width: 100%;
        min-width: 200px;

        .evr-media-object__content {
          .evr-media-object__title {
            margin-bottom: var(--evr-spacing-4xs);
          }

          .evr-media-object__subtitle {
            color: var(--evr-content-primary-lowemp);
            font-size: var(--evr-font-size-sm);
          }
        }
      }

      // Hover state for menu items
      &:hover {
        background-color: var(--evr-color-neutral-50);
      }

      // Focus state for menu items
      &:focus,
      &.hover {
        background-color: var(--evr-color-neutral-100);
        outline: 2px solid var(--evr-color-focus);
        outline-offset: -2px;
      }

      // Active/selected state
      &.active {
        background-color: var(--evr-color-primary-50);
        border-left: 3px solid var(--evr-color-primary-500);
      }
    }
  }
}

// Responsive adjustments for smaller screens
@media (max-width: 768px) {
  .tag-popover-menu-prototype {
    :global(.evr-tag.info) {
      font-size: var(--evr-font-size-xs) !important;
      padding: var(--evr-spacing-5xs) var(--evr-spacing-4xs) !important;
    }

    &__header {
      padding: var(--evr-spacing-xs) var(--evr-spacing-sm);
      
      h4 {
        font-size: var(--evr-font-size-md);
      }
    }

    :global(.evr-menu-list) {
      max-height: 250px;

      :global(.evr-menu-list-item-base) {
        padding: var(--evr-spacing-4xs) var(--evr-spacing-xs);

        .evr-media-object {
          min-width: 180px;
        }
      }
    }
  }
}
