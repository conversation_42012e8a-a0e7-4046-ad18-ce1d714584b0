.evrSimpleFilterTagGroupChild {
  width: fit-content;
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: var(--evr-spacing-2xs);
  padding: var(--evr-spacing-3xs) 0;
  margin: 0;

  div {
    // prevent text in the Tags from wrapping
    white-space: nowrap;
  }

  li {
    list-style-type: none;
  }

  &.showAll {
    flex-wrap: wrap;
  }
  
  .baseButtonOverrides {
    min-width: fit-content;
    white-space: nowrap;
  }
}

.hiddenMeasurementContainer {
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    height: 0;
    overflow: hidden;
}
