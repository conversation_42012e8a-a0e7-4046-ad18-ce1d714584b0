import React from 'react';
import { ProfileManager } from './ProfileManager';
import { mockManagers } from '../../profile-mock-data';

/**
 * Demo component to test the updated ProfileManager with Tag + Popover
 * This can be used for visual testing and development
 */
export const ProfileManagerDemo: React.FC = () => {
  return (
    <div style={{ padding: '2rem', fontFamily: 'Arial, sans-serif' }}>
      <h2>ProfileManager with Tag + Popover Demo</h2>
      
      <div style={{ marginBottom: '2rem', maxWidth: '400px' }}>
        <h3>Multiple Managers (3 total)</h3>
        <ProfileManager 
          managers={mockManagers} 
          testId="demo-multiple-managers"
        />
      </div>

      <div style={{ marginBottom: '2rem', maxWidth: '400px' }}>
        <h3>Two Managers</h3>
        <ProfileManager 
          managers={mockManagers.slice(0, 2)} 
          testId="demo-two-managers"
        />
      </div>

      <div style={{ marginBottom: '2rem', maxWidth: '400px' }}>
        <h3>Single Manager</h3>
        <ProfileManager 
          managers={mockManagers.slice(0, 1)} 
          testId="demo-single-manager"
        />
      </div>

      <div style={{ marginBottom: '2rem', maxWidth: '400px' }}>
        <h3>No Managers (should not render)</h3>
        <ProfileManager 
          managers={[]} 
          testId="demo-no-managers"
        />
        <span style={{ color: '#666', fontStyle: 'italic' }}>(Nothing should appear above)</span>
      </div>

      <div style={{ marginTop: '3rem', padding: '1rem', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
        <h4>Testing Instructions:</h4>
        <ul>
          <li>For multiple managers, click on the blue count tags (e.g., "+2", "+1") to open the popover</li>
          <li>Verify the popover appears below the tag with proper positioning</li>
          <li>Check that all managers are listed with their names, titles, and avatars</li>
          <li>Click on a manager item to select it (check console for selection log)</li>
          <li>Click outside the popover or press Escape to close it</li>
          <li>Verify focus returns to the tag after closing</li>
          <li>Test keyboard navigation within the menu</li>
          <li>Compare with the original PopoverMenu behavior to ensure feature parity</li>
        </ul>
      </div>
    </div>
  );
};

export default ProfileManagerDemo;
