import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, defaultModes } from '../../../../../chromatic';
import { SimpleFilterCustomControl, SimpleFilterTriggerButton, SimpleFilterPopover } from '../';

<Meta
  title="Testing/Automation Test Cases/SimpleFilterControlGroup/SimpleFilterPopover"
  component={SimpleFilterCustomControl}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
/>

<Canvas>
  <Story name="Right Aligned" >
    {(args) => (
      <div style={{width: '200px', border: '2px dashed lightgrey', paddingInlineStart: '100px'}}>
        <SimpleFilterCustomControl id="basic-example-right" open={true}>
          <SimpleFilterTriggerButton id='trigger-right' ariaControls='popover-right' onClick={console.log}>
            Label
          </SimpleFilterTriggerButton>

          <SimpleFilterPopover id='popover-right' placement='bottomRight'>
            Popover Content
          </SimpleFilterPopover>
        </SimpleFilterCustomControl>
      </div>
    )}

  </Story>
</Canvas>
<Canvas>

  <Story name="Left Aligned">
    {(args) => (
      <div style={{width: '200px', border: '2px dashed lightgrey', paddingInlineStart: '100px'}}>
        <SimpleFilterCustomControl id="basic-example-left" open={true}>
          <SimpleFilterTriggerButton id='trigger-left' ariaControls='popover-left' onClick={console.log}>
            Label
          </SimpleFilterTriggerButton>

          <SimpleFilterPopover id='popover-left'>
            Popover Content
          </SimpleFilterPopover>
        </SimpleFilterCustomControl>
      </div>
    )}

  </Story>
</Canvas>
