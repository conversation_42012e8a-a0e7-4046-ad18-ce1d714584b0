import React, { useRef, useState } from 'react';
import { Popover, Tag, MenuList, MenuListItem, MediaObject, Avatar } from '@ceridianhcm/components';
import { IManagerProfile } from '../../profile-mock-data';
import './tag-popover-menu-prototype.scss';

/**
 * Props for the TagPopoverMenuPrototype component
 */
export interface TagPopoverMenuPrototypeProps {
  /** Array of managers to display in the popover menu */
  managers: IManagerProfile[];
  /** Optional test ID for testing purposes */
  testId?: string;
  /** Optional ID for the component */
  id?: string;
}

/**
 * TagPopoverMenuPrototype demonstrates using a Tag component as a trigger for a Popover
 * containing a MenuList with manager information. This prototype replaces the PopoverMenu
 * approach with a more flexible Tag + Popover combination.
 *
 * Key features:
 * - Tag component as custom trigger (instead of built-in PopoverMenu triggers)
 * - Manual state management for open/close
 * - MenuList + MenuListItem for menu functionality
 * - Proper accessibility with focus management
 * - Custom styling for primary blue count badge appearance
 *
 * @example
 * ```tsx
 * const managers = [
 *   { id: 1, name: '<PERSON>', title: 'Manager', avatar: 'path/to/avatar.jpg' },
 *   { id: 2, name: 'Jane Smith', title: 'Senior Manager', avatar: 'path/to/avatar2.jpg' }
 * ];
 *
 * <TagPopoverMenuPrototype managers={managers} testId="tag-popover-prototype" />
 * ```
 */
export const TagPopoverMenuPrototype: React.FC<TagPopoverMenuPrototypeProps> = ({
  managers,
  testId = 'tag-popover-prototype',
  id = 'tag-popover-prototype',
}) => {
  // Ref to the Tag component for triggerRef
  const tagRef = useRef<any>(null);
  
  // State to control popover visibility
  const [open, setOpen] = useState(false);

  // Only render if there are multiple managers (same logic as original)
  if (managers.length <= 1) {
    return null;
  }

  /**
   * Handle Tag click to toggle popover
   */
  const handleTagClick = () => {
    setOpen(!open);
  };

  /**
   * Handle popover close with proper focus management
   */
  const handleClose = () => {
    setOpen(false);
    // Return focus to the trigger element for accessibility
    if (tagRef.current?.tag) {
      tagRef.current.tag.focus();
    }
  };

  /**
   * Handle manager selection from the menu
   */
  const handleManagerSelect = ({ id: selectedId }: { id: string }) => {
    console.log('Manager selected:', selectedId);
    setOpen(false);
    // Return focus to trigger after selection
    if (tagRef.current?.tag) {
      tagRef.current.tag.focus();
    }
  };

  return (
    <div className="tag-popover-menu-prototype" data-testid={testId}>
      <Tag
        ref={tagRef}
        id={`${id}-tag-trigger`}
        label={`+${managers.length - 1}`}
        onClick={handleTagClick}
        status="info" // Will be styled to primary blue via CSS
        ariaLabel={`View all ${managers.length} managers`}
        testId={`${testId}-tag-trigger`}
      />
      
      <Popover
        id={`${id}-popover`}
        open={open}
        triggerRef={{ current: tagRef.current?.tag || null }}
        placement="bottomCenter"
        onClose={handleClose}
        ariaLabelledBy={`${id}-popover-header`}
        testId={`${testId}-popover`}
      >
        <div 
          id={`${id}-popover-header`} 
          className="tag-popover-menu-prototype__header"
        >
          <h4 className="evrHeading4">All Managers</h4>
        </div>
        
        <MenuList
          id={`${id}-menu-list`}
          ariaLabel="Managers list"
          onChange={handleManagerSelect}
          testId={`${testId}-menu-list`}
        >
          {managers.map((manager) => (
            <MenuListItem 
              key={manager.id} 
              id={`manager-${manager.id}`}
              testId={`${testId}-manager-${manager.id}`}
            >
              <MediaObject
                media={
                  <Avatar
                    id={`manager-avatar-${manager.id}`}
                    ariaLabel={`${manager.name} Avatar`}
                    size="md"
                    src={manager.avatar}
                    testId={`${testId}-avatar-${manager.id}`}
                  />
                }
                id={`manager-media-object-${manager.id}`}
                title={<h4 className="evrHeading4">{manager.name}</h4>}
                subtitle={manager.title}
                gap="--evr-spacing-sm"
                mediaAlignment="center"
              />
            </MenuListItem>
          ))}
        </MenuList>
      </Popover>
    </div>
  );
};

export default TagPopoverMenuPrototype;
