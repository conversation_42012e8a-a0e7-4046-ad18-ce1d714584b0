.edit-profile-form {
  &__content {
    // Additional spacing for edit profile form specific content
    .form-field-container {
      margin-bottom: var(--evr-spacing-lg);
    }
  }

  &__photo-section {
    margin-bottom: var(--evr-spacing-xl);
    padding-bottom: var(--evr-spacing-lg);
    border-bottom: 1px solid var(--evr-border-neutral-default);
    display: flex;
    align-items: center;
    gap: var(--evr-spacing-md);
  }

  &__photo-actions {
    display: flex;
    flex-direction: column;
    gap: var(--evr-spacing-sm);
    align-items: flex-start;
    flex-wrap: wrap;
  }

  &__pronouns-section {
    margin-bottom: var(--evr-spacing-xl);
    padding-bottom: var(--evr-spacing-lg);
    border-bottom: 1px solid var(--evr-border-neutral-default);
  }

  &__biography-section {
    margin-bottom: var(--evr-spacing-xl);
    padding-bottom: var(--evr-spacing-lg);
    border-bottom: 1px solid var(--evr-border-neutral-default);
  }
}

// Ensure TextArea has proper height for biography content
.edit-profile-form textarea {
  min-height: 120px;
  resize: vertical;
}
