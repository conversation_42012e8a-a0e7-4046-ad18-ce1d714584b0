import React from 'react';
import { TagPopoverMenuPrototype } from './TagPopoverMenuPrototype';
import { mockManagers } from '../../profile-mock-data';

/**
 * Demo component to test the TagPopoverMenuPrototype
 * This can be used for visual testing and development
 */
export const TagPopoverMenuDemo: React.FC = () => {
  return (
    <div style={{ padding: '2rem', fontFamily: 'Arial, sans-serif' }}>
      <h2>Tag Popover Menu Prototype Demo</h2>
      
      <div style={{ marginBottom: '2rem' }}>
        <h3>Multiple Managers (3 total)</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <span><PERSON><PERSON></span>
          <TagPopoverMenuPrototype 
            managers={mockManagers} 
            testId="demo-multiple-managers"
            id="demo-multiple-managers"
          />
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h3>Two Managers</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <span><PERSON><PERSON></span>
          <TagPopoverMenuPrototype 
            managers={mockManagers.slice(0, 2)} 
            testId="demo-two-managers"
            id="demo-two-managers"
          />
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h3>Single Manager (should not render)</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <span>Macie Burke</span>
          <TagPopoverMenuPrototype 
            managers={mockManagers.slice(0, 1)} 
            testId="demo-single-manager"
            id="demo-single-manager"
          />
          <span style={{ color: '#666', fontStyle: 'italic' }}>(No tag should appear)</span>
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h3>Many Managers (10 total)</h3>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <span>Primary Manager</span>
          <TagPopoverMenuPrototype 
            managers={[
              ...mockManagers,
              { id: 4, name: 'Alice Johnson', title: 'Team Lead', avatar: 'assets/images/user-cage.png' },
              { id: 5, name: 'Bob Wilson', title: 'Senior Manager', avatar: 'assets/images/user-taro.jpg' },
              { id: 6, name: 'Carol Davis', title: 'Director', avatar: 'assets/images/user-cage.png' },
              { id: 7, name: 'David Brown', title: 'VP', avatar: 'assets/images/user-taro.jpg' },
              { id: 8, name: 'Eve Miller', title: 'Manager', avatar: 'assets/images/user-cage.png' },
              { id: 9, name: 'Frank Garcia', title: 'Team Lead', avatar: 'assets/images/user-taro.jpg' },
              { id: 10, name: 'Grace Lee', title: 'Senior Director', avatar: 'assets/images/user-cage.png' },
            ]} 
            testId="demo-many-managers"
            id="demo-many-managers"
          />
        </div>
      </div>

      <div style={{ marginTop: '3rem', padding: '1rem', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
        <h4>Testing Instructions:</h4>
        <ul>
          <li>Click on the blue count tags (e.g., "+2", "+1", "+9") to open the popover</li>
          <li>Verify the popover appears below the tag</li>
          <li>Check that all managers are listed with their names, titles, and avatars</li>
          <li>Click on a manager item to select it (check console for selection log)</li>
          <li>Click outside the popover or press Escape to close it</li>
          <li>Verify focus returns to the tag after closing</li>
          <li>Test keyboard navigation within the menu</li>
        </ul>
      </div>
    </div>
  );
};

export default TagPopoverMenuDemo;
