import { Meta, Story, Canvas, ArgsTable } from '@storybook/addon-docs';
import { useArgs } from '@storybook/client-api';
import { SimpleFilterToggle } from './simple-filter-toggle';

import Examples from './simple-filter-toggle.examples.mdx';

<Meta
  title="Components/SimpleFilter/SimpleFilterControlGroup/Templates/SimpleFilterToggle"
  component={SimpleFilterToggle}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/kzT3cDVCr1jKuF9ypizkTE/%F0%9F%A7%AA-Simple-Filters?node-id=9246-13343&node-type=frame&m=dev',
    },
  }}
  argTypes={{
    id: {
      type: 'string',
      description: 'A unique identifier.',
    },
    testId: {
      type: 'string',
      description: 'An id used for automation testing.',
    },
    selected: {
      description: 'Sets the button to selected state.',
      table: {
        defaultValue: { summary: false },
      },
    },
  }}
  args={{
    id: 'basic-id',
    testId: 'basic-test-id',
  }}
/>

<Examples/>

## Live Demo

<Canvas>
  <Story name="SimpleFilterToggle">
    {(args) => {
      const [{ selected }, updateArgs] = useArgs();

      return (
        <SimpleFilterToggle {...args} onClick={() => updateArgs({ selected: !selected })}>
          Approved (5)
        </SimpleFilterToggle>
      );
    }}

  </Story>
</Canvas>

<ArgsTable story="SimpleFilterToggle" />
