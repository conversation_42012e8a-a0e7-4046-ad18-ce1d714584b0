import React, { forwardRef, PropsWithChildren } from 'react';

import { SimpleFilterTriggerButton } from '../simple-filter-trigger-button/simple-filter-trigger-button';

export interface ISimpleFilterToggle {
  id: string;
  onClick: () => void;
  selected?: boolean;
  testId?: string;
}

export const SimpleFilterToggle = forwardRef<HTMLButtonElement, PropsWithChildren<ISimpleFilterToggle>>(
  ({ id, testId, children, onClick, selected = false }, ref) => {
    return (
      <SimpleFilterTriggerButton id={id} testId={testId} onClick={onClick} selected={selected} ref={ref}>
        {children}
      </SimpleFilterTriggerButton>
    );
  }
);

SimpleFilterToggle.displayName = 'SimpleFilterToggle';
