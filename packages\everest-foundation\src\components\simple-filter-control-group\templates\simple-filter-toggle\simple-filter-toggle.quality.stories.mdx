import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, defaultModes } from '../../../../../chromatic';
import { SimpleFilterToggle } from './simple-filter-toggle';

<Meta
  title="Testing/Automation Test Cases/SimpleFilterControlGroup/SimpleFilterToggle"
  component={SimpleFilterToggle}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
/>

<Canvas>
  <Story name="Selected">
    {(args) => (
      <SimpleFilterToggle id="toggle-1" selected>
        Toggle Content
      </SimpleFilterToggle>
    )}
  </Story>
</Canvas>
