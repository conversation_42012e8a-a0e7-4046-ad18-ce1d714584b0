import React, { PropsWithChildren, useCallback, useEffect, useRef, useState } from 'react';

import styles from './simple-filter-control-group.module.scss';

export interface ISimpleFilterControlGroup {
  /** Sets `id` attribute for the group */
  id: string;
  /** Sets `aria-label` for the group */
  ariaLabel: string;
  /** Sets `data-testid` for testing */
  testId?: string;
  /** Callback for when the number of visible controls changes */
  onFiltersCountChange?: (filtersCount: number) => void;
}

export const SimpleFilterControlGroup = ({
  id,
  ariaLabel,
  testId,
  children,
  onFiltersCountChange,
}: PropsWithChildren<ISimpleFilterControlGroup>): JSX.Element => {
  const parentDivRef = useRef<HTMLDivElement>(null);
  const childDivRef = useRef<HTMLDivElement>(null);
  const [displayedItemsCount, setDisplayedItemsCount] = useState(React.Children.count(children));
  const [itemWidths, setItemWidths] = useState<number[]>([]);

  // dictates when new controls are added or removed from the view
  const onContainerResize = useCallback(() => {
    if (!parentDivRef.current || !childDivRef.current) return;

    const containerWidth = parentDivRef.current.clientWidth;
    const isContentOverflowing = parentDivRef.current.scrollWidth > containerWidth;
    const flexGapWidth = parseFloat(window.getComputedStyle(childDivRef.current).rowGap);

    // overflowing. Remove item if exists
    if (isContentOverflowing && displayedItemsCount > 0) {
      setDisplayedItemsCount((prevCount) => {
        onFiltersCountChange?.(prevCount - 1);
        return prevCount - 1;
      });
    } else if (displayedItemsCount < itemWidths.length) {
      const newItemWidth = itemWidths[displayedItemsCount];

      // this translates to: current container width with current items and gaps in it + new item + required flex gap
      const canFitNewItem = childDivRef.current.clientWidth + newItemWidth + flexGapWidth < containerWidth;

      // add item if it will fit
      if (canFitNewItem) {
        setDisplayedItemsCount((prevCount) => {
          onFiltersCountChange?.(prevCount + 1);
          return prevCount + 1;
        });
      }
    }
  }, [displayedItemsCount, itemWidths, onFiltersCountChange]);

  // reacts to children changes and updates the widths
  useEffect(() => {
    if (childDivRef.current) {
      const childrenElementWidths = Array.from(childDivRef.current.children)
        .map((child) => (child as HTMLElement).offsetWidth)
        .filter((val) => val !== 0); // This filtering is necessary to remove any "ghost" elements that get added to the DOM by FocusRing in the ButtonBase which messes up the calculations

      // This will update the width of only the item that changed width when it becomes un/selected
      setItemWidths((oldValues: number[]) => {
        const updatedItems = [...oldValues];

        childrenElementWidths.forEach((value, ind) => {
          if (updatedItems[ind] === undefined || updatedItems[ind] !== value) {
            updatedItems[ind] = value;
          }
        });

        return updatedItems;
      });
    }
  }, [children]);

  useEffect(() => {
    const observer = new ResizeObserver(onContainerResize);
    if (parentDivRef.current) {
      observer.observe(parentDivRef.current);
    }
    return () => observer.disconnect();
  }, [onContainerResize]);

  return (
    // In this setup there are two wrapping divs:
    // * the parent div takes all the available space of the container it's in
    // * the child div that shrinks to only fit the controls passed as children
    <div ref={parentDivRef} id={id} data-testid={testId} aria-label={ariaLabel} role="region">
      <div ref={childDivRef} className={styles.evrSimpleFilterControlGroupChild}>
        {React.Children.toArray(children).slice(0, displayedItemsCount)}
      </div>
    </div>
  );
};

SimpleFilterControlGroup.displayName = 'SimpleFilterControlGroup';
