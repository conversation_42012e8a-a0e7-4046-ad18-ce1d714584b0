import React, { PropsWithChildren, useContext, forwardRef } from 'react';
import classNames from 'classnames';

import { mergeRefs } from '../../../../utils';
import { ButtonBase } from '../../../button-base';
import { Icon } from '../../../icon';
import { SimpleFilterCustomControlContext } from '../simple-filter-custom-control/simple-filter-custom-control.context';

import styles from './simple-filter-trigger-button.module.scss';

export interface ISimpleFilterTriggerButton {
  id: string;
  onClick: () => void;
  selected?: boolean;
  selectionCount?: number;
  open?: boolean;
  testId?: string;
  ariaControls?: string;
}

export const SimpleFilterTriggerButton = forwardRef<HTMLButtonElement, PropsWithChildren<ISimpleFilterTriggerButton>>(
  ({ id, testId, children, onClick, selected, selectionCount, ariaControls }, ref): JSX.Element => {
    const { open, triggerRef } = useContext(SimpleFilterCustomControlContext);

    return (
      <ButtonBase
        id={id}
        testId={testId}
        onClick={onClick}
        variant="secondaryNeutral"
        type="button"
        className={classNames(styles.buttonBaseOverrides, {
          [styles.selected]: selected,
          [styles.dropdownTriggerButtonOverrides]: open !== undefined,
        })}
        ariaPressed={selectionCount !== undefined ? undefined : selected}
        ref={mergeRefs([ref, triggerRef])}
        ariaHasPopup={open !== undefined ? 'true' : undefined}
        ariaExpanded={open}
        ariaControls={ariaControls}
      >
        {selected && <Icon testId={testId ? `${testId}-checkmark` : undefined} name="check" />}
        {children}
        {selectionCount !== undefined && open !== undefined && (
          <span className={styles.selectionCountBadge}>{selectionCount}</span>
        )}
        {open !== undefined && (
          <Icon
            testId={testId ? `${testId}-chevron` : undefined}
            name={open === true ? 'chevronUpSmall' : 'chevronDownSmall'}
          />
        )}
      </ButtonBase>
    );
  }
);

SimpleFilterTriggerButton.displayName = 'SimpleFilterTriggerButton';
