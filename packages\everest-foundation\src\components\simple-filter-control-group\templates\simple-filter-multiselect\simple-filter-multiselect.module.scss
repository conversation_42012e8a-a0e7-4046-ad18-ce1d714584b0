@use '../../../../index.scss' as helper;
@use '@ceridianhcm/theme/dist/scss/' as typography;

$list-item-height: var(--evr-size-md);
$offset-for-focus-ring: var(--evr-spacing-3xs);

.evrContentWrapper {
  display: flex;
  flex-direction: column;
  // This margin/padding "magic" is needed, because OverlayBody (Popover) comes with built-in horizontal padding that we need to reduce. (this css could be simplified if we get rid of it)
  // In addition we need a dedicated 4px padding for the <ul> to make sure the outline around each option does not get cut off.
  padding-block-start: calc(var(--evr-spacing-sm) - var(--evr-spacing-3xs));
  padding-block-end: var(--evr-spacing-sm);
  margin-inline-start: calc(-1 * $offset-for-focus-ring);
  gap: var(--evr-spacing-xs);
  min-width: calc(helper.applyRemFactor(15rem) - (2 * var(--evr-size-sm)) + $offset-for-focus-ring);
  max-width: calc(helper.applyRemFactor(30rem) - (2 * var(--evr-size-sm)) + $offset-for-focus-ring);

  .buttonsWrapper {
    display: flex;
    gap: var(--evr-spacing-2xs);
    justify-content: end;
  }

  .listWrapper {
    margin: 0;
    padding: $offset-for-focus-ring; // give the list extra padding for the outline to make sure it won't be cut off
    display: flex;
    flex-direction: column;
    gap: var(--evr-spacing-2xs);
    max-height: calc(10 * $list-item-height); // temporary. Waiting for guidance from UX
    overflow-y: auto;
  }

  .listItem {
    @include typography.body1Regular;
    
    color: var(--evr-content-primary-default);
    display: flex;
    align-items: center;
    box-sizing: border-box;
    gap: var(--evr-spacing-2xs); 
    flex-basis: $list-item-height;
    outline: none;
    padding-inline-end: $offset-for-focus-ring; // adds a bit of space so that focus ring isn't too close to end of text
    
    &:hover {
      cursor: pointer;
    }

    @include helper.focusRingOutline(true);
  }
  
  .checkboxOverrides {
    align-self: start;
  }
  
  .srOnly {
    @include helper.visuallyHidden();
  }
}

