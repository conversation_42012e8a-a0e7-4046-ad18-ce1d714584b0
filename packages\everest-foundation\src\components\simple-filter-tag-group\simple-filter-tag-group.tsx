import React, { PropsWithChildren, useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import classNames from 'classnames';

import { announce, templateReplacer } from '../../utils';
import { ButtonBase } from '../button-base';

import styles from './simple-filter-tag-group.module.scss';

export interface SimpleFilterTagGroupProps {
  /** Sets `id` attribute for the group */
  id: string;
  /** Sets `data-testid` for testing */
  testId?: string;
  /** Callback for when the Clear all button gets activated */
  onClearAll: () => void;
  /** Collection of labels and messages used within the component */
  textMap: {
    /** Label for the "+X more" button */
    showXMoreButtonLabel: string;
    /** Label for the "Show less" button */
    showLessButtonLabel: string;
    /** Label for the "Clear all" button */
    clearAllButtonLabel: string;
    /** Text for the announcement made after the "Clear all" button is activated */
    filtersClearedMessage: string;
  };
}

export const SimpleFilterTagGroup = ({
  id,
  testId,
  children,
  textMap,
  onClearAll,
}: PropsWithChildren<SimpleFilterTagGroupProps>): JSX.Element => {
  const parentDivRef = useRef<HTMLDivElement>(null);
  const childDivRef = useRef<HTMLUListElement>(null);
  const showMoreButtonRef = useRef<HTMLButtonElement>(null);
  const hiddenMeasurementRef = useRef<HTMLDivElement>(null);
  const childrenCount = React.Children.count(children);

  const [tagWidths, setTagWidths] = useState<number[]>([]);
  const [displayedTagsCount, setDisplayedTagsCount] = useState(childrenCount || 0);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  const onContainerResize = useCallback(() => {
    if (!parentDivRef.current || !childDivRef.current || tagWidths?.length === 0) return;

    const containerWidth = parentDivRef.current.clientWidth;
    const containerScrollWidth = parentDivRef.current.scrollWidth;
    const isContentOverflowing = parentDivRef.current.scrollWidth > containerWidth;
    const flexGapWidth = parseFloat(window.getComputedStyle(childDivRef.current).rowGap);
    const toggleWidth = showMoreButtonRef.current?.offsetWidth || 0;

    if (isContentOverflowing && displayedTagsCount > 0) {
      setDisplayedTagsCount((prevCount) => {
        let newDisplayedTagsCount = prevCount;
        let accumulatedWidth = 0;
        while (
          newDisplayedTagsCount > 1 &&
          containerScrollWidth - containerWidth > accumulatedWidth + tagWidths[newDisplayedTagsCount - 1] + flexGapWidth
        ) {
          accumulatedWidth += tagWidths[newDisplayedTagsCount - 1] + flexGapWidth;
          newDisplayedTagsCount -= 1;
        }
        return newDisplayedTagsCount - 1;
      });
    } else if (displayedTagsCount < tagWidths.length) {
      const newItemWidth = tagWidths[displayedTagsCount];
      const isLastItem = displayedTagsCount + 1 === childrenCount;

      const canFitNewItem =
        childDivRef.current.clientWidth + newItemWidth + flexGapWidth - (isLastItem ? toggleWidth : 0) < containerWidth;
      if (canFitNewItem) {
        setDisplayedTagsCount((prevCount) => prevCount + 1);
      }
    }
  }, [displayedTagsCount, tagWidths, childrenCount]);

  // update tag widths when children change
  useLayoutEffect(() => {
    if (!hiddenMeasurementRef.current) return;

    const widths = Array.from(hiddenMeasurementRef.current.children).map((child) => (child as HTMLElement).offsetWidth);
    setTagWidths(widths);
  }, [children]);

  // detects changes in width
  useEffect(() => {
    const observer = new ResizeObserver(onContainerResize);
    if (parentDivRef.current) {
      observer.observe(parentDivRef.current);
    }
    return () => observer.disconnect();
  }, [onContainerResize, isExpanded]);

  const getToggleButtonLabel = () => {
    if (textMap.showXMoreButtonLabel && textMap.showLessButtonLabel) {
      return templateReplacer(isExpanded ? textMap.showLessButtonLabel : textMap.showXMoreButtonLabel, [
        (childrenCount - displayedTagsCount).toString(),
      ]);
    }
  };

  return (
    <div ref={parentDivRef} id={id} data-testid={testId} role="group">
      {childrenCount > 0 && (
        <ul
          ref={childDivRef}
          className={classNames(styles.evrSimpleFilterTagGroupChild, {
            [styles.showAll]: isExpanded,
          })}
        >
          {isExpanded
            ? React.Children.map(children, (child) =>
                React.isValidElement(child) ? <li key={child.key}>{child}</li> : <li key={String(child)}>{child}</li>
              )
            : React.Children.toArray(children)
                .slice(0, displayedTagsCount)
                .map((child) =>
                  React.isValidElement(child) ? <li key={child.key}>{child}</li> : <li key={String(child)}>{child}</li>
                )}

          {displayedTagsCount < childrenCount && (
            <li>
              <ButtonBase
                id={`${id}-visibility-toggle-button`}
                testId={testId ? `${testId}-visibility-toggle-button` : undefined}
                ref={showMoreButtonRef}
                variant="tertiaryNeutral"
                onClick={() => setIsExpanded(!isExpanded)}
                callToAction
                size="small"
                className={styles.baseButtonOverrides}
              >
                {getToggleButtonLabel()}
              </ButtonBase>
            </li>
          )}
          {childrenCount > 0 && (
            <li>
              <ButtonBase
                id={`${id}-clear-all-button`}
                testId={testId ? `${testId}-clear-all-button` : undefined}
                variant="tertiaryNeutral"
                onClick={() => {
                  onClearAll();
                  setIsExpanded(false);
                  announce(textMap.filtersClearedMessage, 'assertive');
                }}
                callToAction
                size="small"
                className={styles.baseButtonOverrides}
              >
                {textMap.clearAllButtonLabel}
              </ButtonBase>
            </li>
          )}
        </ul>
      )}
      {/* to prevent flickering of the tags when UI changes we measure the widths in a hidden container.
      We only rerender if the children change */}
      <div ref={hiddenMeasurementRef} className={styles.hiddenMeasurementContainer} aria-hidden="true">
        {children}
      </div>
    </div>
  );
};
