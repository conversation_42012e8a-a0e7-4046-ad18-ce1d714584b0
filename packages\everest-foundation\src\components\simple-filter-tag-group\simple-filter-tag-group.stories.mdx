import { Meta, <PERSON>, Canvas, ArgsTable } from '@storybook/addon-docs';
import { action } from '@storybook/addon-actions';
import { SimpleFilterTagGroup } from './simple-filter-tag-group';
import Examples from './simple-filter-tag-group.examples.mdx';
import { Tag } from '../tag';

<Meta
  title="Components/SimpleFilter/SimpleFilterTagGroup"
  component={SimpleFilterTagGroup}
  parameters={{
    status: {
      type: 'ready',
    },
    controls: {
      sort: 'requiredFirst',
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/kzT3cDVCr1jKuF9ypizkTE/%F0%9F%A7%AA-Simple-Filters?node-id=9246-13343&node-type=frame&m=dev',
    },
  }}
  args={{
    id: 'basic-simple-filter-control-group-demo',
    testId: 'basic-simple-filter-control-group-demo-test-id',
    textMap: {
      showXMoreButtonLabel: '+{0} more',
      showLessButtonLabel: 'Show less',
      clearAllButtonLabel: 'Clear all',
      filtersClearedMessage: 'Filters cleared',
    },
  }}
/>

# SimpleFilterTagGroup

<Examples />

## Live Demo

<Canvas>
  <Story name="SimpleFilterTagGroup">
    {(args) => {
      const [tags, setTags] = React.useState([
        { id: 1, label: 'South America' },
        { id: 2, label: 'Europe' },
        { id: 3, label: 'North America' },
        { id: 4, label: 'Africa' },
        { id: 5, label: 'Asia' },
        { id: 6, label: 'Australia' },
        { id: 7, label: 'Antarctica' },
      ]);
      const handleClearAll = () => {
        action('onClearAll')();
        setTags([]);
      };
      const handleRemoveTag = (id) => {
        setTags((prevTags) => prevTags.filter((tag) => tag.id !== id));
      };
      return (
        <div style={{ width: '70%', width: '100%', border: '2px dashed lightgrey' }}>
          <SimpleFilterTagGroup {...args} onClearAll={handleClearAll}>
            {tags.map((tag, index) => (
              <Tag
                key={`${index}-${tag.id}`}
                label={tag.label}
                onRemove={() => handleRemoveTag(tag.id)}
                removeButtonAriaLabel={`Remove filter ${tag.label}`}
              />
            ))}
          </SimpleFilterTagGroup>
        </div>
      );
    }}
  </Story>
</Canvas>

## Props

<ArgsTable story="SimpleFilterTagGroup" />

### `textMap`

| Property                  | Type     | Description                                                   |
| ------------------------- | -------- | ------------------------------------------------------------- |
| **showXMoreButtonLabel**  | `string` | Label for the button that shows hidden tags.                  |
| **showLessButtonLabel**   | `string` | Label for the button to collapse the tags.                    |
| **clearAllButtonLabel**   | `string` | Label for the "Clear All" button.                             |
| **filtersClearedMessage** | `string` | Screen reader message announced when all filters are cleared. |
