import { SimpleFilterMultiSelect } from './';
import { CodeExample } from '../../../../../.storybook/doc-blocks/example/example';
import { Button } from '../../../button';

export const scope = {
  SimpleFilterMultiSelect,
  Button
}

## SimpleFilterMultiSelect

The `SimpleFilterMultiSelect` is a child component of the `SimpleFilterControlGroup`, designed to allow users select multiple filtering options from the list. 
In order for filtering to be executed the user needs to confirm selection by clicking Apply button.

export const defaultCode = `() => {
  const [selectedOptions, setSelectedOptions] = React.useState([]);

  // Wrap your options in useMemo to ensure the objects references will not change
  const options = React.useMemo(
    () => [
      { id: 1, label: 'Sales'},
      { id: 2, label: 'Marketing'},
      { id: 3, label: 'Tech Support'},
      { id: 4, label: 'Human Resources'},
      { id: 5, label: 'Product & Technology'},
    ],
    []
  );
    
  const onApplyHandler = (e) => {
    setSelectedOptions(e);
    console.log(e);
  }
  return (
    <SimpleFilterMultiSelect
      id='department-selection-menu'
      testId='department-selection-menu-test-id'
      options={options}
      onApply={onApplyHandler}
      selectedOptions={selectedOptions}
      textMap={{
        applyButtonLabel:'Apply',
        clearButtonLabel: 'Clear',
        listAriaLabel: 'Department options',
        selectionClearedMessage: 'Selection cleared'
      }}
    >
      Departments
    </SimpleFilterMultiSelect>
}`;

<CodeExample scope={scope} code={defaultCode} />

### Reference to the Trigger Button

`SimpleFilterMultiSelect` provides programatic element access via `ref`. This can be useful when the trigger button needs to be focused programatically.

export const multiselectWithRef = `() => {
  const multiselectRef = React.useRef<ISimpleFilterMultiSelectRef>(null);

  const [selectedOptions, setSelectedOptions] = React.useState([]);

  // Wrap your options in useMemo to ensure the objects references will not change
  const options = React.useMemo(
    () => [
      { id: 1, label: 'Sales'},
      { id: 2, label: 'Marketing'},
      { id: 3, label: 'Tech Support'},
      { id: 4, label: 'Human Resources'},
      { id: 5, label: 'Product & Technology'},
    ],
    []
  );
    
  const onApplyHandler = (e) => {
    setSelectedOptions(e);
    console.log(e);
  }
  return (
    <div style={{display: 'flex', gap: '16px'}}>
      <Button label="Log the ref" onClick={() => console.log(multiselectRef)} />
      <SimpleFilterMultiSelect
        ref={multiselectRef}
        id='department-selection-menu'
        testId='department-selection-menu-test-id'
        options={options}
        onApply={onApplyHandler}
        selectedOptions={selectedOptions}
        textMap={{
          applyButtonLabel:'Apply',
          clearButtonLabel: 'Clear',
          listAriaLabel: 'Department options',
          selectionClearedMessage: 'Selection cleared'
        }}
      >
        Departments
      </SimpleFilterMultiSelect>
    </div>
}`;

<CodeExample scope={scope} code={multiselectWithRef} />