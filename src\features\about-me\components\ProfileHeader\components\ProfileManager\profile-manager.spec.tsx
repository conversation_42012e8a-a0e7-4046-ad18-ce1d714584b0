import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ProfileManager } from './ProfileManager';

// Mock scrollIntoView which is not available in JSDOM
Object.defineProperty(window.HTMLElement.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true,
});

// Mock data for different scenarios
const mockMultipleManagers = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    title: 'Head of Customer Service',
    avatar: 'assets/images/user-cage.png',
  },
  {
    id: 2,
    name: '<PERSON>',
    title: 'Regional Sales Manager',
    avatar: 'assets/images/user-taro.jpg',
  },
  {
    id: 3,
    name: '<PERSON>',
    title: 'Regional Sales Manager',
    avatar: 'assets/images/user-cage.png',
  },
];

const mockSingleManager = [
  {
    id: 1,
    name: '<PERSON>',
    title: 'Senior Director',
    avatar: 'assets/images/user-1.jpg',
  },
];

const mockEmptyManagers: any[] = [];

// Mock the profile mock data module
jest.mock('../../profile-mock-data', () => ({
  mockBio: {
    managers: [], // Default empty, will be overridden in tests
  },
}));

describe('ProfileManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering with Multiple Managers', () => {
    it('should render with "Managers" label when there are multiple managers', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      expect(screen.getByText('Managers')).toBeInTheDocument();
    });

    it('should display the primary manager information', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      expect(screen.getByText('Macie Burke')).toBeInTheDocument();
      expect(screen.getByText('Head of Customer Service')).toBeInTheDocument();
    });

    it('should render the Tag trigger when there are multiple managers', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      expect(screen.getByTestId('managers-tag-trigger')).toBeInTheDocument();
      expect(screen.getByText('+2')).toBeInTheDocument(); // Shows +2 for 3 total managers (1 primary + 2 others)
    });

    it('should render manager avatar with correct props', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      const avatar = screen.getByLabelText('Manager Avatar');
      expect(avatar).toBeInTheDocument();
    });

    it('should show only 2 additional managers in Popover when Tag is clicked', async () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      // Primary manager should be displayed in the main MediaObject
      expect(screen.getByText('Macie Burke')).toBeInTheDocument();
      expect(screen.getByText('Head of Customer Service')).toBeInTheDocument();

      // Click the Tag trigger to open the popover
      const tagTrigger = screen.getByTestId('managers-tag-trigger');
      fireEvent.click(tagTrigger);

      // Wait for popover to appear and check that only additional managers are shown
      await waitFor(() => {
        expect(screen.getByTestId('managers-popover')).toBeInTheDocument();
      });

      // Only the first 2 additional managers should be available as menu items (excluding primary)
      expect(screen.queryByTestId('manager-item-1')).not.toBeInTheDocument(); // Macie Burke (primary) should not be in popover
      expect(screen.getByTestId('manager-item-2')).toBeInTheDocument(); // Erik Johansson
      expect(screen.getByTestId('manager-item-3')).toBeInTheDocument(); // Mary Shaw

      // Verify only additional managers' names are shown in the popover
      expect(screen.getAllByText('Macie Burke')).toHaveLength(1); // Only in main view, not in popover
      expect(screen.getByText('Erik Johansson')).toBeInTheDocument();
      expect(screen.getByText('Mary Shaw')).toBeInTheDocument();
    });

    it('should show only 2 additional managers even when there are more than 3 total managers', async () => {
      // Create mock data with 5 managers
      const mockManyManagers = [
        { id: 1, name: 'Primary Manager', title: 'CEO', avatar: 'avatar1.jpg' },
        { id: 2, name: 'Manager Two', title: 'VP Sales', avatar: 'avatar2.jpg' },
        { id: 3, name: 'Manager Three', title: 'VP Marketing', avatar: 'avatar3.jpg' },
        { id: 4, name: 'Manager Four', title: 'VP Engineering', avatar: 'avatar4.jpg' },
        { id: 5, name: 'Manager Five', title: 'VP Operations', avatar: 'avatar5.jpg' },
      ];

      render(<ProfileManager managers={mockManyManagers} />);

      // Tag should still show "+2" even with 5 total managers
      expect(screen.getByText('+2')).toBeInTheDocument();

      // Click the Tag trigger to open the popover
      const tagTrigger = screen.getByTestId('managers-tag-trigger');
      fireEvent.click(tagTrigger);

      // Wait for popover to appear
      await waitFor(() => {
        expect(screen.getByTestId('managers-popover')).toBeInTheDocument();
      });

      // Only the first 2 additional managers should be shown (index 1 and 2)
      expect(screen.queryByTestId('manager-item-1')).not.toBeInTheDocument(); // Primary manager not in popover
      expect(screen.getByTestId('manager-item-2')).toBeInTheDocument(); // Manager Two
      expect(screen.getByTestId('manager-item-3')).toBeInTheDocument(); // Manager Three
      expect(screen.queryByTestId('manager-item-4')).not.toBeInTheDocument(); // Manager Four not shown
      expect(screen.queryByTestId('manager-item-5')).not.toBeInTheDocument(); // Manager Five not shown

      // Verify only the first 2 additional managers are displayed
      expect(screen.getByText('Manager Two')).toBeInTheDocument();
      expect(screen.getByText('Manager Three')).toBeInTheDocument();
      expect(screen.queryByText('Manager Four')).not.toBeInTheDocument();
      expect(screen.queryByText('Manager Five')).not.toBeInTheDocument();
    });

    it('should show "+1" when there are exactly 2 managers', async () => {
      const mockTwoManagers = [
        { id: 1, name: 'Primary Manager', title: 'CEO', avatar: 'avatar1.jpg' },
        { id: 2, name: 'Second Manager', title: 'VP Sales', avatar: 'avatar2.jpg' },
      ];

      render(<ProfileManager managers={mockTwoManagers} />);

      // Tag should show "+1" for 2 total managers (1 primary + 1 additional)
      expect(screen.getByText('+1')).toBeInTheDocument();

      // Click the Tag trigger to open the popover
      const tagTrigger = screen.getByTestId('managers-tag-trigger');
      fireEvent.click(tagTrigger);

      // Wait for popover to appear
      await waitFor(() => {
        expect(screen.getByTestId('managers-popover')).toBeInTheDocument();
      });

      // Only the second manager should be shown in the popover
      expect(screen.queryByTestId('manager-item-1')).not.toBeInTheDocument(); // Primary manager not in popover
      expect(screen.getByTestId('manager-item-2')).toBeInTheDocument(); // Second Manager

      // Verify only the second manager is displayed
      expect(screen.getByText('Second Manager')).toBeInTheDocument();
    });
  });

  describe('Rendering with Single Manager', () => {
    it('should render with "Manager" label when there is only one manager', () => {
      render(<ProfileManager managers={mockSingleManager} />);

      // Look specifically for the h5 element with "Manager" text
      expect(screen.getByRole('heading', { level: 5, name: 'Manager' })).toBeInTheDocument();
    });

    it('should not render Tag trigger when there is only one manager', () => {
      render(<ProfileManager managers={mockSingleManager} />);

      expect(screen.queryByTestId('managers-tag-trigger')).not.toBeInTheDocument();
    });

    it('should display the single manager information', () => {
      render(<ProfileManager managers={mockSingleManager} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Senior Director')).toBeInTheDocument();
    });

    it('should render manager avatar with correct props', () => {
      render(<ProfileManager managers={mockSingleManager} />);

      const avatar = screen.getByLabelText('Manager Avatar');
      expect(avatar).toBeInTheDocument();
    });
  });

  describe('Rendering with No Managers', () => {
    it('should not render anything when there are no managers', () => {
      const { container } = render(<ProfileManager managers={mockEmptyManagers} />);

      expect(container.firstChild).toBeNull();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined managers array gracefully', () => {
      const { container } = render(<ProfileManager managers={undefined} />);

      expect(container.firstChild).toBeNull();
    });

    it('should handle null managers array gracefully', () => {
      const { container } = render(<ProfileManager managers={null as any} />);

      expect(container.firstChild).toBeNull();
    });

    it('should use mock data when no managers prop is provided', () => {
      // This test verifies the component still works with the original mock data approach
      render(<ProfileManager />);

      // Should render something (either from mock data or handle gracefully)
      const component = screen.queryByTestId('profile-manager');
      // Component should either render with mock data or be null, both are acceptable
      expect(component).toBeDefined();
    });
  });

  describe('Component Structure', () => {
    it('should have proper CSS classes for styling', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      const stackPanelItem = screen.getByText('Managers').closest('.detail-item.manager');
      expect(stackPanelItem).toBeInTheDocument();
    });

    it('should have proper component structure', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      // Check that the main container has the correct CSS classes
      const stackPanelItem = screen.getByText('Managers').closest('.detail-item.manager');
      expect(stackPanelItem).toBeInTheDocument();

      // Check that the Tag trigger is present for multiple managers
      expect(screen.getByTestId('managers-tag-trigger')).toBeInTheDocument();
    });

    it('should render MediaObject with correct props', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      // Check that MediaObject is rendered with expected content
      expect(screen.getByText('Macie Burke')).toBeInTheDocument();
      expect(screen.getByText('Head of Customer Service')).toBeInTheDocument();
    });

    it('should render with testId prop', () => {
      render(<ProfileManager managers={mockSingleManager} testId="custom-test-id" />);

      expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
    });
  });
});
