import React from 'react';
import { render, screen, act } from '@testing-library/react';

import { SimpleFilterControlGroup } from './simple-filter-control-group';
import { SimpleFilterCustomControl, SimpleFilterMultiSelect, SimpleFilterToggle } from './templates/';

jest.mock('./templates', () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  SimpleFilterCustomControl: () => <div>SimpleFilterCustomControl</div>,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  SimpleFilterMultiSelect: () => <div>SimpleFilterMultiSelect</div>,
  // eslint-disable-next-line @typescript-eslint/naming-convention
  SimpleFilterToggle: () => <div>SimpleFilterToggle</div>,
}));

describe('[SimpleFilterControlGroup]', () => {
  const defaultProps = {
    id: 'id',
    testId: 'test-id',
    ariaLabel: 'Aria Label',
    onFiltersCountChange: jest.fn(),
  };
  const getGroup = () => screen.getByTestId(defaultProps.testId);

  let resizeObserverTrigger: jest.Mock;

  beforeEach(() => {
    resizeObserverTrigger = jest.fn();

    global.ResizeObserver = jest.fn((callback) => {
      resizeObserverTrigger = callback;
      return {
        observe: jest.fn(),
        unobserve: jest.fn(),
        disconnect: jest.fn(),
      };
    }) as jest.Mock;
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  const renderComponent = (addInvalidChild = false) =>
    render(
      <div id="container" style={{ width: '300px' }}>
        <SimpleFilterControlGroup {...defaultProps} onFiltersCountChange={console.log}>
          <SimpleFilterCustomControl id="custom-id" testId="custom-test-id" />
          <SimpleFilterToggle id="toggle-id" testId="toggle-test-id" onClick={jest.fn()} />
          <SimpleFilterMultiSelect
            id="popover-id"
            testId="popover-test-id"
            options={[]}
            onApply={jest.fn()}
            textMap={{
              applyButtonLabel: 'applyButtonLabel',
              clearButtonLabel: 'clearButtonLabel',
              listAriaLabel: 'listAriaLabel',
              selectionClearedMessage: 'selectionCleared',
            }}
          />
          {addInvalidChild && <div>Invalid Child</div>}
        </SimpleFilterControlGroup>
      </div>
    );

  it('renders children', () => {
    renderComponent();

    expect(screen.getByTestId(defaultProps.testId)).toBeInTheDocument();
    expect(screen.getByText('SimpleFilterCustomControl')).toBeInTheDocument();
    expect(screen.getByText('SimpleFilterToggle')).toBeInTheDocument();
    expect(screen.getByText('SimpleFilterMultiSelect')).toBeInTheDocument();
  });

  it('calss onFiltersCountChange when the number of visible filters changes', () => {
    const onFiltersCountChangeMock = jest.fn();

    const { rerender } = render(
      <SimpleFilterControlGroup {...defaultProps} onFiltersCountChange={onFiltersCountChangeMock}>
        <SimpleFilterCustomControl id="SimpleFilterCustomControl" />
        <SimpleFilterToggle id="SimpleFilterToggle" onClick={jest.fn()} />
      </SimpleFilterControlGroup>
    );

    act(() => {
      // adjust the widths so that the onFiltersCountChangeMock will fire
      const parent = document.getElementById(defaultProps.id);
      Object.defineProperty(parent, 'clientWidth', { value: 300 });
      Object.defineProperty(parent, 'scrollWidth', { value: 400 });
      // trigger resize. SimpleFilterControlGroup doesn't actually read the args from it so we can pass empty object
      resizeObserverTrigger([{}]);
    });

    rerender(
      <SimpleFilterControlGroup {...defaultProps} onFiltersCountChange={onFiltersCountChangeMock}>
        <SimpleFilterCustomControl id="SimpleFilterCustomControl" />
      </SimpleFilterControlGroup>
    );

    expect(onFiltersCountChangeMock).toHaveBeenCalledWith(1);
  });

  it('applies aria attributes correctly', () => {
    renderComponent();

    expect(getGroup()).toHaveAttribute('aria-label', defaultProps.ariaLabel);
    expect(getGroup()).toHaveAttribute('role', 'region');
  });
});
