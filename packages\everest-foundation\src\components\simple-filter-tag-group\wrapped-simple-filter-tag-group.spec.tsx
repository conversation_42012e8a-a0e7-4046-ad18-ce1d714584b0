import React from 'react';
import { render, screen } from '@testing-library/react';

import { WrappedSimpleFilterTagGroup } from './wrapped-simple-filter-tag-group';

// adding this test only to satisfy the coverage report
describe('WrappedSimpleFilterTagGroup', () => {
  beforeEach(() => {
    global.ResizeObserver = jest.fn(() => {
      return {
        observe: jest.fn(),
        unobserve: jest.fn(),
        disconnect: jest.fn(),
      };
    });
  });

  it('renders SimpleFilterTagGroup with the correct number of children', () => {
    render(<WrappedSimpleFilterTagGroup id="test-group" testId="test-id" childrenCount={3} />);

    expect(screen.getByTestId('test-id')).toBeInTheDocument();
    expect(screen.getAllByRole('listitem')).toHaveLength(4); // Clear All button is also a list item
  });

  it('renders correctly with zero children', () => {
    render(<WrappedSimpleFilterTagGroup id="empty-group" testId="empty-test" childrenCount={0} />);

    expect(screen.getByTestId('empty-test')).toBeInTheDocument();
    expect(screen.queryAllByRole('listitem')).toHaveLength(0);
  });
});
