.manager-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--evr-spacing-sm);

  h5 {
    margin: 0;
    margin-right: var(--evr-spacing-xs);
  }

  // Ensure proper spacing between label and count badge
  .managers-count-badge {
    // Apply spacing class for consistent positioning
    &--spacing-xs {
      margin-left: var(--evr-spacing-xs);
    }
  }
}

// Style the popover menu button to look like a tag
.detail-item.manager {
  // Target the popover menu button within the MediaObject title
  .evr-media-object__title {
    display: flex;
    align-items: center;
    gap: var(--evr-spacing-xs);

    // Style the segmentedInformational popover button to look like a tag
    :global(.evr-popover-menu) {
      :global(.evr-popover-menu-button) {
        // Override segmentedInformational styles to look like an info tag
        background-color: var(--evr-color-informational-100) !important;
        color: var(--evr-color-informational-700) !important;
        border: 1px solid var(--evr-color-informational-200) !important;
        border-radius: var(--evr-border-radius-sm) !important;
        padding: var(--evr-spacing-4xs) var(--evr-spacing-xs) !important;
        font-size: var(--evr-font-size-sm) !important;
        font-weight: var(--evr-demi-bold-weight) !important;
        line-height: var(--evr-line-height-sm) !important;
        min-height: auto !important;
        height: auto !important;

        &:hover {
          background-color: var(--evr-color-informational-200) !important;
          color: var(--evr-color-informational-800) !important;
        }

        &:focus {
          outline: 2px solid var(--evr-color-focus) !important;
          outline-offset: 2px !important;
        }

        &:active {
          background-color: var(--evr-color-informational-300) !important;
        }
      }
    }
  }
}
