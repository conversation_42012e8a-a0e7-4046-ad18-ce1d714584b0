.manager-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--evr-spacing-sm);

  h5 {
    margin: 0;
    margin-right: var(--evr-spacing-xs);
  }

  // Ensure proper spacing between label and count badge
  .managers-count-badge {
    // Apply spacing class for consistent positioning
    &--spacing-xs {
      margin-left: var(--evr-spacing-xs);
    }
  }
}

// Style the popover menu button to look like a primary blue tag
.detail-item.manager {
  // Target the popover menu button within the MediaObject title
  .evr-media-object__title {
    display: flex;
    align-items: center;
    gap: var(--evr-spacing-sm); // Increased spacing between name and tag

    // Style the segmentedInformational popover button to look like a primary blue tag
    :global(.evr-popover-menu) {
      // Ensure proper positioning relative to trigger
      position: relative;

      :global(.evr-popover-menu-button) {
        // Override segmentedInformational styles to look like a primary blue tag
        background-color: var(--evr-color-primary-500) !important;
        color: var(--evr-content-primary-inverse) !important;
        border: 1px solid var(--evr-color-primary-500) !important;
        border-radius: var(--evr-border-radius-md) !important; // More rounded corners
        padding: var(--evr-spacing-4xs) var(--evr-spacing-xs) !important;
        font-size: var(--evr-font-size-sm) !important;
        font-weight: var(--evr-demi-bold-weight) !important;
        line-height: var(--evr-line-height-sm) !important;
        min-height: auto !important;
        height: auto !important;
        margin-left: var(--evr-spacing-xs); // Additional spacing from the name

        // Ensure proper vertical alignment
        display: inline-flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: var(--evr-color-primary-600) !important;
          border-color: var(--evr-color-primary-600) !important;
          color: var(--evr-content-primary-inverse) !important;
        }

        &:focus {
          outline: 2px solid var(--evr-color-focus) !important;
          outline-offset: 2px !important;
          background-color: var(--evr-color-primary-500) !important;
        }

        &:active {
          background-color: var(--evr-color-primary-700) !important;
          border-color: var(--evr-color-primary-700) !important;
        }
      }

      // Ensure popover is properly positioned relative to the trigger
      :global(.evr-popover) {
        z-index: 1000;
      }
    }
  }

  // Ensure proper spacing for the MediaObject content
  .evr-media-object {
    .evr-media-object__content {
      .evr-media-object__title {
        margin-bottom: var(--evr-spacing-4xs);
      }

      .evr-media-object__subtitle {
        color: var(--evr-content-primary-lowemp);
        font-size: var(--evr-font-size-sm);
      }
    }
  }
}
