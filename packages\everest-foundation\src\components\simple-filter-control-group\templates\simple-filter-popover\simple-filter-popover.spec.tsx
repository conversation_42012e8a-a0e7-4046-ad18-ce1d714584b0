import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { SimpleFilterPopover } from './simple-filter-popover';
import { SimpleFilterCustomControlContext } from '../simple-filter-custom-control/simple-filter-custom-control.context';

describe('[SimpleFilterPopover]', () => {
  const defaultProps = {
    id: 'test-popover',
    testId: 'test-popover-id',
    onClose: jest.fn(),
    onOpen: jest.fn(),
    children: 'Popover Content',
  };

  const getPopoverContent = () => screen.getByText(defaultProps.children);
  const getPopover = () => screen.queryByRole('dialog');

  const renderComponent = (props = {}, contextValue = {}) =>
    render(
      <SimpleFilterCustomControlContext.Provider value={{ triggerRef: { current: null }, open: true, ...contextValue }}>
        <SimpleFilterPopover {...defaultProps} {...props} />
      </SimpleFilterCustomControlContext.Provider>
    );

  it('does not render if open is false', () => {
    renderComponent({}, { open: false });

    expect(getPopover()).not.toBeInTheDocument();
  });

  it('renders with the correct id and testId', () => {
    renderComponent();

    expect(getPopover()).toBeInTheDocument();
    expect(getPopover()).toHaveAttribute('id', defaultProps.id);
    expect(getPopover()).toHaveAttribute('data-testid', defaultProps.testId);
  });

  it('renders the children inside', () => {
    renderComponent();

    expect(getPopoverContent()).toBeInTheDocument();
  });

  it('triggers onClose callback when closing', async () => {
    renderComponent();

    await userEvent.keyboard('{Escape}');
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('triggers onOpen callback when opening', () => {
    renderComponent();

    expect(defaultProps.onOpen).toHaveBeenCalled();
  });
});
