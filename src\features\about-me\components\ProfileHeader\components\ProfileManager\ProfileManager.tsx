import React, { useRef, useState } from 'react';
import { StackPanelItem } from '@ceridianhcm/everest-cdk';
import { MediaObject, Avatar, Popover, Tag, MenuList, MenuListItem } from '@ceridianhcm/components';
import { mockBio, IManagerProfile } from '../../profile-mock-data';
import './profile-manager.scss';

/**
 * Props for the ProfileManager component
 */
export interface ProfileManagerProps {
  /** Array of managers to display. If not provided, uses mock data */
  managers?: IManagerProfile[];
  /** Test ID for testing purposes */
  testId?: string;
}

/**
 * ProfileManager displays the primary manager information with a clickable count tag
 * when there are multiple managers. The tag opens a popover menu showing all managers.
 */
export const ProfileManager: React.FC<ProfileManagerProps> = ({
  managers: propManagers,
  testId = 'profile-manager',
}) => {
  const managers = propManagers || mockBio.managers || [];
  const primaryManager = managers[0];
  const hasMultipleManagers = managers.length > 1;

  // Ref to the Tag component for triggerRef
  const tagRef = useRef<any>(null);

  // State to control popover visibility
  const [open, setOpen] = useState(false);

  // If no managers, don't render anything
  if (!primaryManager) {
    return null;
  }

  /**
   * Handle Tag click to toggle popover
   */
  const handleTagClick = () => {
    setOpen(!open);
  };

  /**
   * Handle popover close with proper focus management
   */
  const handleClose = () => {
    setOpen(false);
    // Return focus to the trigger element for accessibility
    if (tagRef.current?.tag) {
      tagRef.current.tag.focus();
    }
  };

  /**
   * Handle manager selection from the menu
   */
  const handleManagerSelect = ({ id: selectedId }: { id: string }) => {
    console.log('Manager selected:', selectedId);
    setOpen(false);
    // Return focus to trigger after selection
    if (tagRef.current?.tag) {
      tagRef.current.tag.focus();
    }
  };

  // Create the title with the clickable count tag if there are multiple managers
  const managerTitle = hasMultipleManagers ? (
    <>
      <h4 className="evrHeading4">
        <span className="evrPading-xs">{primaryManager.name}</span>
        <Tag
          ref={tagRef}
          id="managers-tag-trigger"
          label={`+${managers.length - 1}`}
          onClick={handleTagClick}
          status="info" // Will be styled to primary blue via CSS
          ariaLabel={`View all ${managers.length} managers`}
          testId="managers-tag-trigger"
        />
      </h4>

      <Popover
        id="managers-popover"
        open={open}
        triggerRef={{ current: tagRef.current?.tag || null }}
        placement="bottomCenter"
        onClose={handleClose}
        ariaLabelledBy="managers-popover-header"
        testId="managers-popover"
      >
        <div id="managers-popover-header" className="profile-manager__popover-header">
          <h4 className="evrHeading4">All Managers</h4>
        </div>

        <MenuList
          id="managers-menu-list"
          ariaLabel="Managers list"
          onChange={handleManagerSelect}
          testId="managers-menu-list"
        >
          {managers.map((manager) => (
            <MenuListItem key={manager.id} id={`manager-${manager.id}`} testId={`manager-item-${manager.id}`}>
              <MediaObject
                media={
                  <Avatar
                    id={`manager-avatar-${manager.id}`}
                    ariaLabel={`${manager.name} Avatar`}
                    size="md"
                    src={manager.avatar}
                    testId={`manager-avatar-${manager.id}`}
                  />
                }
                id={`manager-media-object-${manager.id}`}
                title={<h4 className="evrHeading4">{manager.name}</h4>}
                subtitle={manager.title}
                gap="--evr-spacing-sm"
                mediaAlignment="center"
              />
            </MenuListItem>
          ))}
        </MenuList>
      </Popover>
    </>
  ) : (
    <h4 className="evrHeading4">{primaryManager.name}</h4>
  );

  return (
    <StackPanelItem className="detail-item manager">
      <div data-testid={testId}>
        <h5>{hasMultipleManagers ? 'Managers' : 'Manager'}</h5>
        <MediaObject
          media={<Avatar id="manager-avatar" ariaLabel="Manager Avatar" size="md" src={primaryManager.avatar} />}
          id="manager-media-object"
          title={managerTitle}
          subtitle={primaryManager.title}
          gap="--evr-spacing-sm"
          mediaAlignment="center"
        />
      </div>
    </StackPanelItem>
  );
};

export default ProfileManager;
