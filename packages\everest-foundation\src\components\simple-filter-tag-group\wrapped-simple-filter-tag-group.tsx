import React from 'react';

import { SimpleFilterTagGroup } from './simple-filter-tag-group';

export interface IWrappedSimpleFilterTagGroup {
  id: string;
  testId: string;
  childrenCount: number;
}

export const WrappedSimpleFilterTagGroup = ({
  id,
  testId,
  childrenCount,
}: IWrappedSimpleFilterTagGroup): JSX.Element => {
  return (
    <SimpleFilterTagGroup
      id={id}
      testId={testId}
      textMap={{
        showXMoreButtonLabel: '+{0} more',
        showLessButtonLabel: 'Show less',
        clearAllButtonLabel: 'Clear All',
        filtersClearedMessage: 'All filters have been cleared',
      }}
      onClearAll={console.log}
    >
      {Array.from({ length: childrenCount }, (el, i) => (
        <div key={i} style={{ width: '100px', height: '20px', border: '1px solid lightgreen' }}></div>
      ))}
    </SimpleFilterTagGroup>
  );
};
