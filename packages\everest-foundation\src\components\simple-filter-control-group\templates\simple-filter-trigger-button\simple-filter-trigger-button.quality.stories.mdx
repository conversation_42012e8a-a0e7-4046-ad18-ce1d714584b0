import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, defaultModes } from '../../../../../chromatic';
import { SimpleFilterCustomControl, SimpleFilterTriggerButton, SimpleFilterPopover } from '../';

<Meta
  title="Testing/Automation Test Cases/SimpleFilterControlGroup/SimpleFilterTriggerButton"
  component={SimpleFilterTriggerButton}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
/>

<Canvas>
  <Story name="Basic">
    {(args) => (
      <SimpleFilterTriggerButton id="basic" onClick={console.log}>
        Label
      </SimpleFilterTriggerButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Selected">
    {(args) => (
      <SimpleFilterTriggerButton id="selected" selected onClick={console.log}>
        Label
      </SimpleFilterTriggerButton>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Selection Count and Selected">
    {(args) => (
      <SimpleFilterCustomControl id="selection-count" open>
        <SimpleFilterTriggerButton id="selection-count-and-selected" selectionCount={3} selected onClick={console.log}>
          Label
        </SimpleFilterTriggerButton>
      </SimpleFilterCustomControl>
    )}
  </Story>
</Canvas>
