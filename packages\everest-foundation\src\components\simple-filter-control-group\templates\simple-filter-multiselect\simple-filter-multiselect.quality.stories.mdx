import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, defaultModes } from '../../../../../chromatic';
import { findByRole, screen, userEvent, within } from '@storybook/test';
import { SimpleFilterMultiSelect } from './simple-filter-multiselect';

export const optionsArray = [{ label: 'Option 1' }, { label: 'Option 2' }, { label: 'Option 3' }];

<Meta
  title="Testing/Automation Test Cases/SimpleFilterControlGroup/SimpleFilterMultiSelect"
  component={SimpleFilterMultiSelect}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
  args={{
    textMap: {
      applyButtonLabel: 'Apply',
      clearButtonLabel: 'Clear',
      listAriaLabel: 'Options',
      selectionClearedMessage: 'Selection cleared',
    },
    onApply: console.log,
    options: optionsArray,
    selectedOptions: [optionsArray[1]],
    children: 'Options',
  }}
/>

<Canvas>
  <Story name="With selection">
    {(args) => {
      return <SimpleFilterMultiSelect id="selection-menu-1" {...args} />;
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Expanded"
    play={async ({ canvasElement }) => {
      let btn = await findByRole(canvasElement, 'button', { timeout: 3000 });
      await userEvent.click(btn);
    }}
  >
    {(args) => {
      return <SimpleFilterMultiSelect id="selection-menu-2" {...args} />;
    }}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="Focus on apply button"
    play={async ({ canvasElement }) => {
      let trigger = await findByRole(canvasElement, 'button', { timeout: 3000 });
      await userEvent.click(trigger);

      let clearBtn = await screen.findByRole('button', { name: 'Clear' }, { timeout: 3000 });
      await userEvent.click(clearBtn);
    }}

>

    {(args) => {
      return <SimpleFilterMultiSelect id="selection-menu-3" {...args} />;
    }}

  </Story>
</Canvas>

<Canvas>
  <Story
    name="Focus on trigger button after close"
    play={async ({ canvasElement }) => {
      let trigger = await findByRole(canvasElement, 'button', { timeout: 3000 });
      await userEvent.click(trigger);

      let applyBtn = await screen.findByRole('button', { name: 'Apply' }, { timeout: 3000 });
      await userEvent.click(applyBtn);
    }}

>

    {(args) => {
      return (
        <SimpleFilterMultiSelect
          id='selection-menu-4'
          {...args}
        />
      )
    }}

  </Story>
</Canvas>

<Canvas>
  <Story name="[Playwright] - Interactions" parameters={{ chromatic: Chromatic.DISABLE }}>
    {(args) => {
      return <SimpleFilterMultiSelect id="selection-menu-5" testId="selection-menu-test-id-5" {...args} />;
    }}
  </Story>
</Canvas>
