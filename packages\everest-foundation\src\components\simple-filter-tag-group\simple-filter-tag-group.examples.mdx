import { SimpleFilterTagGroup } from './simple-filter-tag-group';
import { Meta, ArgsTable } from '@storybook/addon-docs';

import { CodeExample } from '../../../.storybook/doc-blocks/example/example.tsx';

import { Tag } from '../tag/tag.tsx';

export const scope = { SimpleFilterTagGroup, Tag };

The `SimpleFilterTagGroup` component is designed to dynamically display a group of tags within a container that adjusts its content visibility based on available space.

### Key Features

- **Dynamic Adjustment**: Automatically adjusts the number of visible tags based on the available horizontal space.
- **Expandable/Collapsible Tags**: Allows controlling visibility of tags that span across multiple rows with a toggle button.
- **Clear All Functionality**: A button to clear all tags.

## Usage

export const defaultCode = `
  () => {
    const [tags, setTags] = useState([
      { id: 1, label: 'Tag 1' },
      { id: 2, label: 'Tag 2' },
      { id: 3, label: 'Tag 3' },
      { id: 4, label: 'Tag 4' }
    ]);
    const removeTag = (tag) => {
      setTags(tags.filter(t => t.id !== tag.id));
    };
    return(
      <div style={{paddingInline: '40px', width: '100%', overflow: 'hidden'}}>
        <SimpleFilterTagGroup id="example-tag-group" onClearAll={() => setTags([])} textMap={{
          showXMoreButtonLabel: '+{0} more',
          showLessButtonLabel: 'Show less',
          clearAllButtonLabel: 'Clear all',
          filtersClearedMessage: 'Filters cleared'
        }}>
          {tags.map(tag => (
            <Tag key={tag.id} label={tag.label} onRemove={() => removeTag(tag)} />
          ))}
        </SimpleFilterTagGroup>
      </div>
    );
  } 
`;

<CodeExample scope={scope} code={defaultCode} />

## Accessibility

- The `filtersClearedMessage` value is <strong>required</strong> for an accessible screen reader experience.
- When the "Clear All" button is clicked or when the last Tag is removed, the focus should be moved to an element that instantiates the Tags such as the first Simple Filter control in the `SimpleFilterControlGroup`.
