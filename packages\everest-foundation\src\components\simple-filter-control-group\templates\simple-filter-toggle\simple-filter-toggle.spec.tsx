import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { SimpleFilterToggle } from './simple-filter-toggle';

describe('[SimpleFilterToggle]', () => {
  const defaultProps = {
    id: 'el-id',
    testId: 'test-id',
    selected: true,
    onClick: jest.fn(),
    children: 'Test Filter',
  };

  const getButton = () => screen.getByRole('button', { name: defaultProps.children });

  it('renders the component with children', () => {
    render(<SimpleFilterToggle {...defaultProps} />);

    expect(getButton()).toBeInTheDocument();
  });

  it('calls onClick when clicked', async () => {
    render(<SimpleFilterToggle {...defaultProps} />);

    await userEvent.click(getButton());

    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  it('applies the aria-pressed prop correctly', () => {
    render(<SimpleFilterToggle {...defaultProps} />);

    expect(getButton()).toHaveAttribute('aria-pressed', 'true');
  });

  it('assigns the correct testId if provided', () => {
    render(<SimpleFilterToggle {...defaultProps} />);

    const button = screen.getByTestId(defaultProps.testId);
    expect(button).toBeInTheDocument();
  });
});
