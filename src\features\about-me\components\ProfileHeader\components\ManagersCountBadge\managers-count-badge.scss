.managers-count-badge {
  display: inline-flex;
  align-items: center;
  position: relative;

  // Spacing following Contact Information component standards
  &--spacing-xs {
    margin-left: var(--evr-spacing-xs);
  }

  &--spacing-sm {
    margin-left: var(--evr-spacing-sm);
  }

  &--spacing-md {
    margin-left: var(--evr-spacing-md);
  }

  // Count badge styling using Everest design tokens
  &__count {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: var(--evr-color-primary-500);
    color: var(--evr-content-primary-inverse);
    border-radius: var(--evr-border-radius-full);
    font-size: var(--evr-font-size-xs);
    font-weight: var(--evr-demi-bold-weight);
    line-height: var(--evr-line-height-sm);
    min-width: var(--evr-spacing-md);
    height: var(--evr-spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--evr-spacing-4xs);
    border: 2px solid var(--evr-color-neutral-0);
    z-index: 1;

    // Ensure proper contrast and accessibility
    &:focus-visible {
      outline: 2px solid var(--evr-color-focus);
      outline-offset: 2px;
    }
  }

  // Hover states for better user experience
  &:hover {
    .managers-count-badge__count {
      background-color: var(--evr-color-primary-600);
    }
  }

  // Active states
  &:active {
    .managers-count-badge__count {
      background-color: var(--evr-color-primary-700);
    }
  }

  // Disabled state if needed
  &--disabled {
    opacity: var(--evr-opacity-disabled);
    pointer-events: none;

    .managers-count-badge__count {
      background-color: var(--evr-color-neutral-300);
      color: var(--evr-content-primary-disabled);
    }
  }

  // Responsive adjustments for smaller screens
  @media (max-width: 768px) {
    &__count {
      font-size: var(--evr-font-size-2xs);
      min-width: var(--evr-spacing-sm);
      height: var(--evr-spacing-sm);
    }
  }
}

// Popover menu item styling for manager entries
.managers-count-badge {
  // Override default popover menu item padding for better manager display
  :global(.evr-popover-menu-item) {
    padding: var(--evr-spacing-xs) var(--evr-spacing-sm);

    // Ensure proper spacing for MediaObject within popover items
    .evr-media-object {
      width: 100%;
      min-width: 200px;

      .evr-media-object__content {
        .evr-media-object__title {
          margin-bottom: var(--evr-spacing-4xs);
        }

        .evr-media-object__subtitle {
          color: var(--evr-content-primary-lowemp);
          font-size: var(--evr-font-size-sm);
        }
      }
    }
  }

  // Hover state for popover menu items
  :global(.evr-popover-menu-item:hover) {
    background-color: var(--evr-color-neutral-50);
  }

  // Focus state for popover menu items
  :global(.evr-popover-menu-item:focus) {
    background-color: var(--evr-color-neutral-100);
    outline: 2px solid var(--evr-color-focus);
    outline-offset: -2px;
  }
}
