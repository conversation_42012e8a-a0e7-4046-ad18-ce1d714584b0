import React, { createRef } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { SimpleFilterTriggerButton } from './simple-filter-trigger-button';
import { SimpleFilterCustomControlContext } from '../simple-filter-custom-control/simple-filter-custom-control.context';

describe('[SimpleFilterTriggerButton]', () => {
  const defaultProps = {
    id: 'id',
    testId: 'test-id',
    onClick: jest.fn(),
    children: 'Filter Button',
  };
  const checkmarkTestid = `${defaultProps.testId}-checkmark`;
  const chevronTestid = `${defaultProps.testId}-chevron`;

  const getButton = () => screen.getByRole('button');
  const getChevron = () => screen.getByTestId(chevronTestid);
  const getCheckmark = () => screen.getByTestId(checkmarkTestid);

  const renderComponent = (props = {}, contextValue = {}) =>
    render(
      <SimpleFilterCustomControlContext.Provider value={{ triggerRef: { current: null }, ...contextValue }}>
        <SimpleFilterTriggerButton {...defaultProps} {...props} />
      </SimpleFilterCustomControlContext.Provider>
    );

  it('renders the button with the provided text', () => {
    renderComponent();

    expect(getButton()).toBeInTheDocument();
    expect(screen.getByText(defaultProps.children)).toBeInTheDocument();
  });

  it('renders the button with the correct ref', () => {
    const ref = createRef<HTMLButtonElement>();

    renderComponent({ ref });

    expect(getButton()).toBeInTheDocument();
    expect(ref.current).toBe(getButton());
  });

  it('renders with the correct id and testId', () => {
    renderComponent();

    expect(getButton()).toHaveAttribute('id', defaultProps.id);
    expect(getButton()).toHaveAttribute('data-testid', defaultProps.testId);
  });

  it('triggers the onClick function when clicked', async () => {
    const onClickMock = jest.fn();
    renderComponent({ onClick: onClickMock });

    await userEvent.click(getButton());

    expect(onClickMock).toHaveBeenCalledTimes(1);
  });

  it('sets aria-pressed=true when selected is true', () => {
    renderComponent({ selected: true });

    expect(getButton()).toHaveAttribute('aria-pressed', 'true');
  });

  it('sets aria-pressed=false when selected is false', () => {
    renderComponent({ selected: false });

    expect(getButton()).toHaveAttribute('aria-pressed', 'false');
  });

  it('does not apply aria-pressed when selected is undefined', () => {
    renderComponent();

    expect(getButton()).not.toHaveAttribute('aria-pressed');
  });

  it('renders the check icon when selected', () => {
    renderComponent({ selected: true });

    expect(getCheckmark()).toBeInTheDocument();
  });

  it('renders the selection count badge when selectionCount is provided and open is defined', () => {
    renderComponent({ selectionCount: 5 }, { open: true });

    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('does not render the selection count badge when selectionCount is not provided', () => {
    renderComponent({}, { open: true });

    expect(screen.queryByText('5')).not.toBeInTheDocument();
  });

  it('does not render the selection count badge when open is undefined', () => {
    renderComponent({ selectionCount: 5 });

    expect(screen.queryByText('5')).not.toBeInTheDocument();
  });

  it('renders the chevron down if closed', () => {
    renderComponent({}, { open: false });

    expect(getChevron()).toHaveAttribute('data-evr-name', 'chevronDownSmall');
  });

  it('renders the chevron up if open', () => {
    renderComponent({}, { open: true });

    expect(getChevron()).toHaveAttribute('data-evr-name', 'chevronUpSmall');
  });

  it('sets aria-haspopup and aria-expanded attributes when open is defined', () => {
    renderComponent({}, { open: true });

    expect(getButton()).toHaveAttribute('aria-haspopup', 'true');
    expect(getButton()).toHaveAttribute('aria-expanded', 'true');
  });

  it('does not apply aria-haspopup or aria-expanded attributes when open is undefined', () => {
    renderComponent();

    expect(getButton()).not.toHaveAttribute('aria-haspopup');
    expect(getButton()).not.toHaveAttribute('aria-expanded');
  });

  it('sets the ref from the context to the button', () => {
    const ref = React.createRef<HTMLButtonElement>();
    renderComponent({}, { triggerRef: ref });

    expect(ref.current).toBe(getButton());
  });
});
