
import { SimpleFilterToggle } from '..';
import { CodeExample } from '../../../../../.storybook/doc-blocks/example/example';

export const scope = { SimpleFilterToggle };

## SimpleFilterToggle

The `SimpleFilterToggle` is a child component of the `SimpleFilterControlGroup`, designed to represent a boolean filter state. 
It provides a simple on/off toggle functionality, allowing users to quickly apply or remove a filter.

### Reference to the Toggle Button

`SimpleFilterToggle` provides programatic element access via `ref`. This can be useful when the button needs to be focused programatically.

export const buttonWithRef = `() => {
    const buttonRef = React.useRef<HTMLButtonElement>(null);

    return (
        <SimpleFilterToggle id="basic-trigger" ref={buttonRef} onClick={() => console.log(buttonRef)}>Click to Log</SimpleFilterToggle>
    );
}`;

<CodeExample scope={scope} code={buttonWithRef} />
