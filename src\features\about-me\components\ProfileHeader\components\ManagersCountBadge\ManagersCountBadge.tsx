import React from 'react';
import { PopoverMenu, PopoverMenuItem, MediaObject, Avatar } from '@ceridianhcm/components';
import { IManagerProfile } from '../../profile-mock-data';
import './managers-count-badge.scss';

/**
 * Props for the ManagersCountBadge component
 */
export interface ManagersCountBadgeProps {
  /** Array of managers to display in the popover menu */
  managers: IManagerProfile[];
  /** Optional test ID for testing purposes */
  testId?: string;
  /** Optional ID for the component */
  id?: string;
}

/**
 * ManagersCountBadge displays a clickable count badge when there are multiple managers.
 * When clicked, it opens a popover menu listing all managers with their details.
 *
 * The badge only appears when there are more than 1 manager, following the requirement
 * to display the count badge only when the total number of managers is greater than 1.
 *
 * @example
 * ```tsx
 * const managers = [
 *   { id: 1, name: '<PERSON>', title: 'Manager', avatar: 'path/to/avatar.jpg' },
 *   { id: 2, name: '<PERSON>', title: 'Senior Manager', avatar: 'path/to/avatar2.jpg' }
 * ];
 *
 * <ManagersCountBadge managers={managers} testId="managers-count-badge" />
 * ```
 */
export const ManagersCountBadge: React.FC<ManagersCountBadgeProps> = ({
  managers,
  testId = 'managers-count-badge',
  id = 'managers-count-badge',
}) => {
  const triggerRef = React.useRef<HTMLButtonElement>(null);

  // Only render the badge if there are more than 1 manager
  if (managers.length <= 1) {
    return null;
  }

  const handleManagerSelect = ({ id: selectedId }: { id: string }) => {
    // Handle manager selection if needed in the future
    console.log('Manager selected:', selectedId);
  };

  return (
    <div className="managers-count-badge" data-testid={testId}>
      <PopoverMenu
        id={`${id}-popover`}
        triggerRef={triggerRef}
        triggerOption="segmentedInformational"
        placement="top"
        triggerProps={{
          variant: 'tertiary',
          size: 'small',
        }}
        buttonAriaLabel={`View all ${managers.length} managers`}
        onChange={handleManagerSelect}
        testId={`${testId}-popover`}
      >
        {managers.map((manager) => (
          <PopoverMenuItem key={manager.id} id={`manager-${manager.id}`} testId={`${testId}-manager-${manager.id}`}>
            <MediaObject
              media={
                <Avatar
                  id={`manager-avatar-${manager.id}`}
                  ariaLabel={`${manager.name} Avatar`}
                  size="md"
                  src={manager.avatar}
                  testId={`${testId}-avatar-${manager.id}`}
                />
              }
              id={`manager-media-object-${manager.id}`}
              title={<h4 className="evrHeading4">{manager.name}</h4>}
              subtitle={manager.title}
              gap="--evr-spacing-sm"
              mediaAlignment="center"
            />
          </PopoverMenuItem>
        ))}
      </PopoverMenu>
      <span className="managers-count-badge__count" aria-label={`${managers.length} managers total`}>
        {managers.length}
      </span>
    </div>
  );
};

export default ManagersCountBadge;
