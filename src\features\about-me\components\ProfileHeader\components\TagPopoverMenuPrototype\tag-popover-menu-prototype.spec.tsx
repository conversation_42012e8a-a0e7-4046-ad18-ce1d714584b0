import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TagPopoverMenuPrototype } from './TagPopoverMenuPrototype';
import { IManagerProfile } from '../../profile-mock-data';

// Mock data for testing
const mockEmptyManagers: IManagerProfile[] = [];

const mockSingleManager: IManagerProfile[] = [
  {
    id: 1,
    name: '<PERSON>',
    title: 'Senior Director',
    avatar: 'assets/images/user-cage.png',
  },
];

const mockMultipleManagers: IManagerProfile[] = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    title: 'Head of Customer Service',
    avatar: 'assets/images/user-cage.png',
  },
  {
    id: 2,
    name: '<PERSON>',
    title: 'Regional Sales Manager',
    avatar: 'assets/images/user-taro.jpg',
  },
  {
    id: 3,
    name: '<PERSON>',
    title: 'Regional Sales Manager',
    avatar: 'assets/images/user-cage.png',
  },
];

describe('TagPopoverMenuPrototype', () => {
  describe('Rendering Logic', () => {
    it('should not render when there are no managers', () => {
      render(<TagPopoverMenuPrototype managers={mockEmptyManagers} testId="test-prototype" />);

      expect(screen.queryByTestId('test-prototype')).not.toBeInTheDocument();
    });

    it('should not render when there is only one manager', () => {
      render(<TagPopoverMenuPrototype managers={mockSingleManager} testId="test-prototype" />);

      expect(screen.queryByTestId('test-prototype')).not.toBeInTheDocument();
    });

    it('should render when there are multiple managers', () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      expect(screen.getByTestId('test-prototype')).toBeInTheDocument();
    });

    it('should display the correct count on the tag', () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      expect(screen.getByText('+2')).toBeInTheDocument(); // 3 total - 1 primary = +2
    });
  });

  describe('Tag Trigger', () => {
    it('should render the Tag component with correct props', () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagTrigger = screen.getByTestId('test-prototype-tag-trigger');
      expect(tagTrigger).toBeInTheDocument();
      expect(tagTrigger).toHaveAttribute('aria-label', 'View all 3 managers');
    });

    it('should have clickable tag with proper role', () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      expect(tagButton).toBeInTheDocument();
    });
  });

  describe('Popover Interactions', () => {
    it('should open popover when tag is clicked', async () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      fireEvent.click(tagButton);

      await waitFor(() => {
        expect(screen.getByTestId('test-prototype-popover')).toBeInTheDocument();
      });
    });

    it('should display popover header', async () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      fireEvent.click(tagButton);

      await waitFor(() => {
        expect(screen.getByText('All Managers')).toBeInTheDocument();
      });
    });

    it('should display all managers in the menu list', async () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      fireEvent.click(tagButton);

      await waitFor(() => {
        expect(screen.getByText('Macie Burke')).toBeInTheDocument();
        expect(screen.getByText('Erik Johansson')).toBeInTheDocument();
        expect(screen.getByText('Mary Shaw')).toBeInTheDocument();
      });
    });

    it('should display manager titles', async () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      fireEvent.click(tagButton);

      await waitFor(() => {
        expect(screen.getByText('Head of Customer Service')).toBeInTheDocument();
        expect(screen.getByText('Regional Sales Manager')).toBeInTheDocument();
      });
    });
  });

  describe('Menu List Interactions', () => {
    it('should render menu list with correct accessibility attributes', async () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      fireEvent.click(tagButton);

      await waitFor(() => {
        const menuList = screen.getByRole('menu', { name: 'Managers list' });
        expect(menuList).toBeInTheDocument();
      });
    });

    it('should handle manager selection', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      fireEvent.click(tagButton);

      await waitFor(() => {
        const managerItem = screen.getByTestId('test-prototype-manager-1');
        fireEvent.click(managerItem);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Manager selected:', 'manager-1');
      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      expect(tagButton).toHaveAttribute('aria-label', 'View all 3 managers');
    });

    it('should have proper test IDs for all interactive elements', async () => {
      render(<TagPopoverMenuPrototype managers={mockMultipleManagers} testId="test-prototype" />);

      // Tag trigger
      expect(screen.getByTestId('test-prototype-tag-trigger')).toBeInTheDocument();

      // Open popover
      const tagButton = screen.getByRole('button', { name: 'View all 3 managers' });
      fireEvent.click(tagButton);

      await waitFor(() => {
        // Popover
        expect(screen.getByTestId('test-prototype-popover')).toBeInTheDocument();
        
        // Menu list
        expect(screen.getByTestId('test-prototype-menu-list')).toBeInTheDocument();
        
        // Manager items
        expect(screen.getByTestId('test-prototype-manager-1')).toBeInTheDocument();
        expect(screen.getByTestId('test-prototype-manager-2')).toBeInTheDocument();
        expect(screen.getByTestId('test-prototype-manager-3')).toBeInTheDocument();
        
        // Avatars
        expect(screen.getByTestId('test-prototype-avatar-1')).toBeInTheDocument();
        expect(screen.getByTestId('test-prototype-avatar-2')).toBeInTheDocument();
        expect(screen.getByTestId('test-prototype-avatar-3')).toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle exactly 2 managers', () => {
      const twoManagers = mockMultipleManagers.slice(0, 2);
      render(<TagPopoverMenuPrototype managers={twoManagers} testId="test-prototype" />);

      expect(screen.getByTestId('test-prototype')).toBeInTheDocument();
      expect(screen.getByText('+1')).toBeInTheDocument(); // 2 total - 1 primary = +1
    });

    it('should handle large number of managers', () => {
      const manyManagers = Array.from({ length: 10 }, (_, index) => ({
        id: index + 1,
        name: `Manager ${index + 1}`,
        title: `Title ${index + 1}`,
        avatar: `avatar-${index + 1}.jpg`,
      }));

      render(<TagPopoverMenuPrototype managers={manyManagers} testId="test-prototype" />);

      expect(screen.getByTestId('test-prototype')).toBeInTheDocument();
      expect(screen.getByText('+9')).toBeInTheDocument(); // 10 total - 1 primary = +9
    });
  });
});
