import { Meta, Story, Canvas } from '@storybook/addon-docs';
import { Chromatic, defaultModes } from '../../../chromatic';
import { findByRole, userEvent } from '@storybook/test';
import { WrappedSimpleFilterTagGroup } from './wrapped-simple-filter-tag-group';

<Meta
  title="Testing/Automation Test Cases/SimpleFilterTagGroup"
  component={WrappedSimpleFilterTagGroup}
  parameters={{
    chromatic: Chromatic.ENABLE_CI,
  }}
/>

<Canvas>
  <Story name="Tags not overflowing">
    {(args) => (
      <div style={{ width: '350px', border: '2px dashed lightgrey' }}>
        <WrappedSimpleFilterTagGroup id="basic-example-1" testId="simple-filter-tag-group" childrenCount={2} />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story name="Tags overflow revealing `+x more` button">
    {(args) => (
      <div style={{ width: '300px', border: '2px dashed lightgrey' }}>
        <WrappedSimpleFilterTagGroup id="basic-example-2" testId="simple-filter-tag-group" childrenCount={3} />
      </div>
    )}
  </Story>
</Canvas>

<Canvas>
  <Story
    name="`+x more` button reveals tags"
    play={async ({ canvasElement }) => {
      let btn = await findByRole(canvasElement, 'button', { name: '+2 more' }, { timeout: 3000 });
      await userEvent.click(btn);
    }}
  >
    {(args) => (
      <div style={{ width: '300px', border: '2px dashed lightgrey' }}>
        <WrappedSimpleFilterTagGroup id="basic-example-3" testId="simple-filter-tag-group" childrenCount={3} />
      </div>
    )}
  </Story>
</Canvas>
