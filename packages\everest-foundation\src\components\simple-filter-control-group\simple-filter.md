# Simple Filter Component

Date: 9.9.24

## Summary

Figma designs: <https://www.figma.com/design/kzT3cDVCr1jKuF9ypizkTE/%F0%9F%A7%AA-Simple-Filters?node-id=9246-13343&node-type=frame&t=JTEDKXJnBBaT1ym5-0>

Simple filter components encapsulate the simple filtering controls in a unified layout.

Given the complexity of managing multiple filtering options we will be using the compound component pattern to maintain clarity and flexibility in the component structure.
This pattern allows for each component to handle their respective logic keeping the implementation simple and separating props.

The key components:

* SimpleFilterControlGroup- the wrapping component that serves as a container for filtering controls
* SimpleFilterTagGroup - the wrapping component that serves as a container for the Tags and the related buttons

Separating components in this manner will allow consumers to fully control their position on the page which is required for designs where other components may be positioned before or after the filtering controls.

Due to space restrictions, the Simple Filter component may not display all necessary filtering controls for the user. Therefore, it is designed to be used in conjunction with another component, such as a filtering panel, which will provide access to additional filtering controls. The architecture of that component is beyond the scope of this architectural proposal.

--

## SimpleFilterControlGroup Component

The SimpleFilterControlGroup component will serve the following functions:

* detecting the children components and their types
* provide necessary layout and spacing
* detect container size changes preventing wrapping of filtering controls
* provide necessary accessibility attributes like `aria-label` and `region`

## SimpleFilterControlGroup Children

The SimpleFilterControlGroup component will allow three types of children to be passed:

* SimpleFilterToggle - A toggle button with "on/off" state used for controlling a boolean-like parameters in the filtering
* SimpleFilterMultiSelect - A menu with multi selection options allowing users to select multiple values
* SimpleFilterCustomControl- A menu-like component allowing consumers to display their custom form control in the popover

API:

* `id` (required) - `id` for the element
* `testId` (optional) - identifier for testing
* `ariaLabel` (optional) - accessible description for the element

### SimpleFilterCustomControlComponent

In some scenarios, consumers may be required to display custom controls. For those situations, we will offer utility components that will help match the style and behavior of other controls.

* `SimpleFilterCustomControl` will be a container component that `SimpleFilterControlGroup` component will use to detect custom content
* `SimpleFilterTriggerButton` will accept properties and children for the trigger button
* `SimpleFilterPopover` will display children in the popover

```jsx
<SimpleFilterControlGroup
  id='sf-id'
  testId='sf-test-id'
  ariaLabel='Simple filters bar'
>
  <SimpleFilterCustomControl
    id="custom-control"
    testId="custom-control-testid"
    open={isPopoverOpen}
  >

    <SimpleFilterTriggerButton
      id="custom-control-button"
      testId="custom-control-button-testid"
      onClick={onClickHandler}
      open={isPopoverOpen} // toggles the chevron
      selected={true} // changes background and adds checkmark
      selectionCount={3} // if > 0 it will display selection count badge
      ariaControls='custom-control-popover'
    >
      My button Label 
    </SimpleFilterTriggerButton>

    <SimpleFilterPopover
      id="custom-control-popover"
      testId="custom-control-popover-testid"
      onClose={() => setIsPopoverOpen(false)}
    >
    // Custom control provided by the consumer
    </SimpleFilterPopover>

  </SimpleFilterCustomControl>

<SimpleFilterControlGroup>
```

### SimpleFilterTriggerButton and SimpleFilterPopover components

The `SimpleFilterTriggerButton` and `SimpleFilterPopover` components will serve as fundamental building blocks for other control types within the `SimpleFilterControlGroup`. They will expose the necessary API for all three types of components that can be displayed inside the `SimpleFilterControlGroup`.

While the `SimpleFilterCustomControl` will provide flexibility for consumers to display custom content, the `SimpleFilterToggle` and `SimpleFilterMultiSelect` will address specific filtering requirements. In addition, both the `SimpleFilterToggle` and the `SimpleFilterMultiSelect` will manage much of the logic for consumers, such as popover open/close state and focus management.

The generic `SimpleFilterCustomControl`, on the other hand, will require consumers to handle these aspects separately, offering increased flexibility in return.

The `SimpleFilterTriggerButton` will internally use our `Button` component passing all the required attributes and props ensuring consistent look and behavior.

The `SimpleFilterPopover` will internally use the `Popover` component.

The full API specs to be determined during the implementation.

### SimpleFilterToggle Component

One of the `SimpleFilterControlGroup` component where users can toggle a single value to true or false. The state of the `SimpleFilterToggle` component will be controlled via two properties:

* `onClick` event - triggered when the user activates the button.
* `selected` property of type boolean, which determines whether the component appears as selected and displays the checkmark icon.

The label of the button is controlled by the consuming app which means that they can provide the number of records in the parenthesis, if they choose to.

To meet accessibility requirements, the component will use the `aria-pressed` attribute, allowing screen readers to correctly announce its current state.

API:

* `id` (required) - `id` for the element
* `testId` (optional) - sets value for the `data-test-id` attribute. Used for testing.
* `selected` (required) - sets the value of the control to true or false
* `onClick` (required) - event that fires afterthe user activates the button.

Example:

```jsx
<SimpleFilterToggle
  id='active-employee-toggle'
  testId='active-employee-toggle-test-id'
  selected={isActiveEmployee}
  onClick={activeEmployeeToggleHandler}
>
  Active Employee
</SimpleFilterToggle>
```

---

### SimpleFilterMultiSelect Component

One of the `SimpleFilterControlGroup` component where users can select from multiple options.

To simplify usage and improve developer experience the `SimpleFilterMultiSelect` component will be a simplified, and not a fully controlled component. Some of the state will be held internally to allow consumers to focus on what's most important to them - value change events and the values.

To update the UI, consumers will use the `selectedOptions` prop. If the `selectedOptions` array has one or more items:

* the component will automatically display a badge showing the number of selected items in the trigger button
* a checkmark will be displayed inside the trigger button.

The popover will also include "Apply" and "Clear" buttons. Selection changes will be passed via `onApply` event as opposed to when individual items are checked. The `onClear` event will not pass any arguments.

---

#### Internal State Management Of The SimpleFilterMultiSelect

Some state will be held internally to simplify usage. For example, the popover's and badge's visibility will be handled internally. In addition selection state of the checkboxes will be handled internally using the `useSelection` hook and the consuming application will be notified about the selection changes when the user clicks the "Apply" button. At that point the `onApply` event will pass the appropriate data and popover will be closed automatically.

#### Internal Components

* The `SimpleFilterPopover` component will handle the display of the dropdown content. It comes with logic handling background clicks.
* Inside the popover, we will NOT be using existing `Checkboxes` due to how A11Y envisions navigation between options. We are required to allow users to navigate the options using arrows. This means that `useKeyboardNavigation` will need to be used to manage focus. The list will have a role set to "listbox" while each row will have a role "option". We will use icons to represent checked and unchecked checkboxes.
* `SimpleFilterTriggerButton` components will be used for the trigger button element.
* Number of selected options will be indicated with an automatically displayed "badge" next to the trigger button's label.

API:

* `id` (required) - `id` for the element
* `testId` (optional) - sets value for th `data-test-id` attribute. Used for testing.
* `options` (required) - an array of options to be displayed in the list. Interface to be defined during the implementation.
* `selectedOptions` (required) - sets the passed options as checked
* `onApply` (required) - event that fires after user clicks the Apply button. Passes selected options.
* `onClear` (required) - event that fires after user clicks the Clear button. It does not need to pass any data.
* `textMap` (required) - a map of text labels for the Apply and Clear buttons

Example:

```jsx
<SimpleFilterMultiSelect
    id='department-selection-menu'
    testId='department-selection-menu-test-id'
    options={departmentOptions}
    selection={departmentSelection}
    onClear={onDepartmentSelectionClearHandler}
    onApply={onDepartmentSelectionApplyHandler} 
    textMap={{
      applyButtonLabel:'Apply',
      clearButtonLabel: 'Clear',
      listAriaLabel: 'Departments options',
      selectionClearedMessage: 'Selection cleared'
    }}
  >
    Departments
  </SimpleFilterMultiSelect>
```

### SimpleFilterControlGroupMobile Layout

The filtering controls within the `SimpleFilterControlGroup` component should not overflow or wrap onto a new line. Instead, when the screen shrinks and the last component cannot fit anymore, that last overflowing component should simply disappear from the UI. The UX's vision is to, at that point, add the control to the "All Filters" side panel.

Since this panel is a separate component that we do not control and we cannot communicate with, the `SimpleFilterControlGroup` component will simply fire an event every time the number of displayed controls changes.
The event will pass a number of displayed controls so that consumers can use that number to determine which of the filters need to be added to the "All Filters" panel.

For example, if `SimpleFilterControlGroup` component gets passed two `SimpleFilterToggles` followed by three `SimpleFilterMultiSelects` as children, the `onFiltersCountChange` will pass 5 as as argument. If, then, user starts to resize the screen and the last `SimpleFilterMultiSelect` gets removed from the UI, the `onFiltersCountChange` will fire with 4.

It is the consumers responsibility to add that last control to the "All Filter" panel when the `onFiltersCountChange` fires.

### SimpleFilterControlGroup Usage Example with All Children Types

Controls will be displayed in the order in which they were passed.

```jsx
<SimpleFilterControlGroup
  id='element-id'
  testId='element-test-id'
  ariaLabel='Simple filters bar'
>

  <SimpleFilterMultiSelect
    id='department-selection-menu'
    testId='department-selection-menu-test-id'
    options={departmentOptions}
    selection={departmentSelection}
    onClear={onDepartmentSelectionClearHandler}
    onApply={onDepartmentSelectionApplyHandler} 
    textMap={{
      applyButtonLabel:'Apply',
      clearButtonLabel: 'Clear',
      listAriaLabel: 'Departments options',
      selectionClearedMessage: 'Selection cleared'
    }}
  >
    Departments
  </SimpleFilterMultiSelect>

  <SimpleFilterToggle
    id='active-employee-toggle'
    testId='active-employee-toggle-test-id'
    selected={isActiveEmployee}
    onClick={activeEmployeeToggleHandler}
  >
    Active Employee
  </SimpleFilterToggle>

  <SimpleFilterCustomControl
    id="custom-control"
    testId="custom-control-testid"
    open={isPopoverOpen}
  >

    <SimpleFilterTriggerButton
      id="custom-control-button"
      testId="custom-control-button-testid"
      onClick={onClickHandler}
      open={isPopoverOpen} // toggles the chevron
      selected={true} // changes background and adds checkmark
      selectionCount={3} // if > 0 it will display selection count badge
      ariaControls='custom-control-popover'
    >
      My button Label 
    </SimpleFilterTriggerButton>

    <SimpleFilterPopover
      id="custom-control-popover"
      testId="custom-control-popover-testid"
      onClose={() => setIsPopoverOpen(false)}
    >
    // Custom control provided by the consumer
    </SimpleFilterPopover>

  </SimpleFilterCustomControl>

<SimpleFilterControlGroup>
```

---

## Filtering State Management

Both `SimpleFilterMultiSelect` and `SimpleFilterToggle` will allow consumers to capture the value/selection change in each of them individually. They can then collect the filtering choices and send them to the backend to re-execute the query.

To assist the collection and storage of these filtering preferences, we will implement a `useSimpleFiltersState` hook. This hook will serve as state management tool that will streamline the usage of the children of `SimpleFilterControlGroup` component and submission of the filtering choices.

### `useSimpleFiltersState` hook

The hook would offer a quick and easy way of managing the state of all the `SimpleFilterControlGroup` controls within a single array.
Consuming apps will be able to connect methods from the hook directly to properties of `SimpleFilterMultiSelect`, `SimpleFilterToggle` and custom controls passed using the `SimpleFilterCustomControl`.

## SimpleFilterControlGroup Usage Example with Children and the useSimpleFiltersState Hook

```jsx
// type definition for the filtering state array
interface ISimpleFilterDataItem {
  name: string; // backend friendly name, e.g. isActive
  label: string; // UI friendly label, e.g. Active Employee
  value: unknown; // this could hold a single value or array of objects representing selected options 
}

const onFiltersChange = (allFiltersState: ISimpleFilterDataItem[]) => {
  // a single callback to re-execute the API call passing the updated parameters
}

const { filters, getFilter, updateFilter, resetFilters } = useSimpleFiltersState(initialState, onFiltersChange);

<SimpleFilterControlGroup
  id='element-id'
  testId='element-test-id'
  ariaLabel='Simple filters bar'
>
  <SimpleFilterMultiSelect
    id='department-selection-menu'
    testId='department-selection-menu-test-id'
    options={departmentOptions}
    selection={filters.getFilter('departments').value}
    onClear={() => updateFilter('departments', [])}
    onApply={(selection) => updateFilter('departments', selection)} 
    textMap={{
      applyButtonLabel:'Apply',
      clearButtonLabel: 'Clear',
      listAriaLabel: 'Departments options',
      selectionClearedMessage: 'Selection cleared'
    }}
  >
    Departments
  </SimpleFilterMultiSelect>

  <SimpleFilterToggle
    id='active-employee-toggle'
    testId='active-employee-toggle-test-id'
    selected={filters.getFilter('isActiveEmployee').value}
    onClick={() => updateFilter('isActiveEmployee', !filters.getFilter('isActiveEmployee').value)}
  >
    Active Employee
  </SimpleFilterToggle>

</SimpleFilterControlGroup>
```

## SimpleFilterTagGroup Component

The `SimpleFilterTagGroup` component will be an independent component and will serve the following functions:

* display and arrange the `Tags` passed in as children
* display the "Clear all" button if at least one `Tag` was passed as children
* measure the `Tags` to make sure they do not wrap on the new line
  * if they need to wrap, hide as many as needed and display the "+X more" button and "Clear all" button at the end
  * if they do not need to wrap, only display the "Clear all" button
* reveal the remaining `Tags` and the "Hide X" button after the "+X more" button is clicked
* display the original `Tags` and "+X more" button after the "Hide X" button is clicked

### Overflow Logic

The `SimpleFilterTag` component will internally handle the visibility of all the `Tags`. Consumers will simply place instances of all the Tags they would like to include. The component will work in a similar fashion to our existing Toolbar component. The measurements will be done on each resize of the container and the appropriate Tags will be displayed/hidden.
Instead of detecting window resize events, we will respond to resizing of the container inside the `SimpleFilterTagGroup`.
We will need to measure each Tag and store the values so than we can check if we have enough space to display it.

### Accessibility Note

* both "+X more" and "Hide" buttons may need additional aria-label to enhance the screen reader users` experience, because it may not be immediately clear what the buttons do based on their short labels. We will request guidance from A11Y team when implementing.

### Basic Usage Example

```jsx
<SimpleFilterTagGroup 
  id='sft-id'
  testId='sft-id'
  ariaLabel='Applied Filters'
  onClearAllClick={clearAllFiltersHandler} // for this we could simply use the "resetFilters" method returned by the useSimpleFiltersState hook 
  textMap={{
    plusXMoreLabel: "+{0} more",
    hideButtonLabel: "Hide",
    clearAllLabel: "Clear All"
  }}
>
  {appliedFilterTags.map((tag, index) => (
    <Tag
      key={index}
      id={tag.id}
      label={`${tag.label}: ${tab.value}`}
      removeButtonAriaLabel={'Remove tag'}
      onRemove={() => removeTagHandler(tag)}
    />
  ))}
</SimpleFilterTagGroup>
```

## Accessibility

* The `SimpleFilterControlGroup` component needs to specify the `role` and `aria-label` for the screen readers to announce the purpose of the component
* The `SimpleFilterMultiSelect` needs to follow similar patterns we set in the `PopoverMenu` setting the required attributes like `aria-expanded` and `aria-haspopup`. The selection list has to use role "listbox" and each item has to have a role set to "option".
* The `SimpleFilterToggle` needs to specify the `aria-pressed` to instruct the screen readers to interpret the button as a toggle button.
* A11Y requested each item in the `SimpleFilterMultiSelect` to be focusable, but navigation should happen with the arrows. This may mean that `aria-activedescendent` may not be necessary. We will have to get guidance from the A11Y team once they try it out.
* In this first iteration all the controls are required to have a visible label so `aria-label` for each control individually is not necessary.
* In this first iteration every control should be focusable using a Tab.

## Future Considerations

* We are allowing the `SimpleFilterControlGroup` component to accept children instead of passing configuration via props to reduce it's internal complexity. This will also make it more readable for the developers and make adding potential new controls easier.
* If A11y team decides that the `SimpleFilterControlGroup` should use `role="toolbar"` and match the behavior of our current Toolbar component then we will create a separate ticket to address that.
* Based on feedback from the consumers and new designs from UX we may be adding more components that may be used as children such as range selectors, radio button groups.

## Other Design Systems

I did not find equivalent of the `SimpleFilterControlGroup` in other libraries.
There are, however, components that are equivalent to `SimpleFilterMultiSelect` and `SimpleFilterToggle`, but since we have the basic building blocks available (`ButtonBase` and `Popover`) we should try to utilize those.

For example, toggle buttons usually use input element with type="checkbox/radio" elements styled as toggle buttons with `aria-checked` set accordingly, but this would require us to create a new component matching the look of a button. Instead, we can use the `ButtonBase`, add the `aria-pressed` and add a custom class setting the "pressed" button styles.

## Required PBIs

* Create the `SimpleFilterControlGroup` and `SimpleFilterCustomControl` + create demo stories and docs
* Create the `SimpleFilterTriggerButton` and `SimpleFilterPopover` + create demo stories and docs
* Create the `SimpleFilterToggle` and create required demo stories + docs
* Create the `SimpleFilterMultiSelect` and create required demo stories + docs
* Create the `useSimpleFiltersState` hook, docs and test
* Create tests for `SimpleFilterControlGroup`, `SimpleFilterCustomControl`,`SimpleFilterTriggerButton` and `SimpleFilterPopover` components
* Create tests for `SimpleFilterToggle` and `SimpleFilterMultiSelect` components
* Create the `SimpleFilterTagGroup` and create required demo stories
* Create tests and documentation for `SimpleFilterTagGroup`
* Add SimpleFilterCustomControldemo to the ref app
* Move all simple filter components to beta stage
* Move all simple filter components to prod stage

## Acceptance Criteria

* Components to be named accordingly
* Build a component and setup in Storybook
* Styles are on par with Design specs in Figma
* Verify all the features work as intended

## Q&A

Q: Will the Apply and Clear buttons always be present in the "bulk filter" component?
A: Clear and Apply will always be there for the `SimpleFilterMultiSelect`

Q: Should there be a maximum number of options the bulk filter menu will display. Right now figma suggests no, but maybe we should have one so that the menu doesn't grow beyond the screen size and we would simply show a scroll bar similar to what Combobox does?
A: UX will provide a default px value. Later on we may allow to override it

Q: Is there any style change on the button if the popover is opened?
A: Chevron gets rotated

## Changes log
